#!/usr/bin/env python
"""
分析和可视化不同消融实验的结果
"""
import os
import re
import glob
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import argparse
from collections import defaultdict

# 解析命令行参数
parser = argparse.ArgumentParser(description='分析注意力机制消融实验结果')
parser.add_argument('--task', type=str, default='single_smp', help='任务名称')
parser.add_argument('--nodes', type=int, default=100, help='节点数量')
args = parser.parse_args()

# 定义结果目录
result_dir = os.path.join(args.task, f"{args.task}{args.nodes}")

# 定义不同的消融实验配置
attention_types = {
    'multi_head': '标准多头注意力',
    'single_head': '单头注意力',
    'no_layer_norm': '无层归一化',
    'no_scale': '无缩放因子',
    'configurable': '可配置头数'
}

# 收集所有实验结果
results = defaultdict(list)
head_counts = defaultdict(list)

# 查找所有实验目录
experiment_dirs = glob.glob(os.path.join(result_dir, "gpnindrnn*"))
for exp_dir in experiment_dirs:
    # 提取注意力类型
    for attn_type in attention_types.keys():
        if attn_type in exp_dir:
            # 读取日志文件
            log_file = os.path.join(exp_dir, "log.txt")
            if os.path.exists(log_file):
                with open(log_file, 'r') as f:
                    log_content = f.read()
                    
                    # 提取头数
                    head_match = re.search(r'n_head: (\d+)', log_content)
                    if head_match:
                        head_count = int(head_match.group(1))
                    else:
                        head_count = 0
                    
                    # 查找最终的奖励值
                    reward_files = glob.glob(os.path.join(exp_dir, "epoch*", "*.png"))
                    if reward_files:
                        # 从文件名中提取奖励值
                        rewards = []
                        for rf in reward_files:
                            reward_match = re.search(r'_(\d+\.\d+)\.png', rf)
                            if reward_match:
                                rewards.append(float(reward_match.group(1)))
                        
                        if rewards:
                            # 使用最后一个epoch的平均奖励
                            avg_reward = np.mean(rewards)
                            results[attn_type].append(avg_reward)
                            head_counts[attn_type].append(head_count)
                            print(f"实验: {os.path.basename(exp_dir)}, 类型: {attn_type}, 头数: {head_count}, 平均奖励: {avg_reward:.4f}")

# 计算每种注意力类型的平均奖励
avg_results = {}
std_results = {}
for attn_type, rewards in results.items():
    if rewards:
        avg_results[attn_type] = np.mean(rewards)
        std_results[attn_type] = np.std(rewards)

# 绘制结果
plt.figure(figsize=(12, 6))

# 条形图 - 不同注意力类型的平均奖励
plt.subplot(1, 2, 1)
if avg_results:
    attn_types = list(avg_results.keys())
    avg_rewards = [avg_results[at] for at in attn_types]
    std_rewards = [std_results[at] for at in attn_types]
    
    x_pos = np.arange(len(attn_types))
    plt.bar(x_pos, avg_rewards, yerr=std_rewards, align='center', alpha=0.7, capsize=10)
    plt.xticks(x_pos, [attention_types[at] for at in attn_types], rotation=45, ha='right')
    plt.ylabel('平均奖励')
    plt.title('不同注意力机制的平均奖励')
    plt.tight_layout()

# 散点图 - 头数与奖励的关系
plt.subplot(1, 2, 2)
for attn_type, rewards in results.items():
    if attn_type == 'configurable' and rewards:
        heads = head_counts[attn_type]
        plt.scatter(heads, rewards, label=attention_types[attn_type], alpha=0.7)

plt.xlabel('注意力头数')
plt.ylabel('奖励')
plt.title('注意力头数与奖励的关系')
plt.legend()
plt.grid(True, linestyle='--', alpha=0.7)
plt.tight_layout()

# 保存图表
plt.savefig('ablation_results.png', dpi=300, bbox_inches='tight')
plt.show()

# 输出结果表格
if avg_results:
    print("\n消融实验结果汇总:")
    print("-" * 50)
    print(f"{'注意力类型':<15} | {'平均奖励':<10} | {'标准差':<10}")
    print("-" * 50)
    
    for attn_type in sorted(avg_results.keys()):
        print(f"{attention_types[attn_type]:<15} | {avg_results[attn_type]:.4f} | {std_results[attn_type]:.4f}")
    
    # 保存为CSV
    df = pd.DataFrame({
        '注意力类型': [attention_types[at] for at in avg_results.keys()],
        '平均奖励': list(avg_results.values()),
        '标准差': list(std_results.values())
    })
    df.to_csv('ablation_results.csv', index=False, encoding='utf-8-sig')
    print("\n结果已保存至 ablation_results.csv 和 ablation_results.png") 