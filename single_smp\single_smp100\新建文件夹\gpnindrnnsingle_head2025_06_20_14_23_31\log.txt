single_smp: 100
model: gpn
rnn: indrnn
hidden_size: 256
batch_size: 32
seed: 12345
train-size: 100000
valid-size: 10000
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 8e-05
critic_lr: 8e-05
attention_type: single_head
n_head: 1
2025_06_20_14_23_31
Batch 99/3125, reward: 0.094, revenue_rate: 0.094, distance: 1.612, power: 0.0232, loss: -0.3077, lr:0.0000778, took: 25.6883s
Batch 199/3125, reward: 0.098, revenue_rate: 0.098, distance: 1.670, power: 0.0194, loss: -1.0472, lr:0.0000714, took: 27.2448s
Batch 299/3125, reward: 0.098, revenue_rate: 0.098, distance: 1.693, power: 0.0205, loss: 0.1369, lr:0.0000616, took: 27.4036s
Batch 399/3125, reward: 0.104, revenue_rate: 0.104, distance: 1.733, power: 0.0142, loss: -1.2803, lr:0.0000495, took: 27.7969s
Batch 499/3125, reward: 0.111, revenue_rate: 0.111, distance: 1.814, power: 0.0112, loss: -0.2152, lr:0.0000363, took: 28.7267s
Batch 599/3125, reward: 0.112, revenue_rate: 0.112, distance: 1.832, power: 0.0073, loss: 0.2034, lr:0.0000236, took: 29.4073s
Batch 699/3125, reward: 0.118, revenue_rate: 0.118, distance: 1.892, power: 0.0037, loss: -0.0862, lr:0.0000128, took: 30.8604s
Batch 799/3125, reward: 0.124, revenue_rate: 0.124, distance: 1.967, power: 0.0007, loss: 0.4219, lr:0.0000051, took: 31.7644s
Batch 899/3125, reward: 0.126, revenue_rate: 0.126, distance: 2.023, power: 0.0015, loss: 0.2111, lr:0.0000013, took: 32.1507s
Batch 999/3125, reward: 0.125, revenue_rate: 0.125, distance: 1.971, power: -0.0071, loss: -0.3301, lr:0.0000798, took: 31.6782s
Batch 1099/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.710, power: -0.1127, loss: 1.0960, lr:0.0000785, took: 28.2401s
Batch 1199/3125, reward: 0.136, revenue_rate: 0.136, distance: 1.569, power: -0.2142, loss: -1.2337, lr:0.0000762, took: 27.0838s
Batch 1299/3125, reward: 0.118, revenue_rate: 0.118, distance: 1.424, power: -0.1695, loss: 0.8095, lr:0.0000729, took: 24.9914s
Batch 1399/3125, reward: 0.078, revenue_rate: 0.078, distance: 1.341, power: 0.0146, loss: -0.2837, lr:0.0000687, took: 22.9752s
Batch 1499/3125, reward: 0.077, revenue_rate: 0.077, distance: 1.338, power: 0.0193, loss: -0.2062, lr:0.0000637, took: 23.1463s
Batch 1599/3125, reward: 0.078, revenue_rate: 0.078, distance: 1.344, power: 0.0159, loss: 0.4578, lr:0.0000580, took: 22.8784s
Batch 1699/3125, reward: 0.090, revenue_rate: 0.090, distance: 1.399, power: -0.0303, loss: 0.8824, lr:0.0000519, took: 23.2939s
Batch 1799/3125, reward: 0.132, revenue_rate: 0.132, distance: 1.399, power: -0.2566, loss: -0.1100, lr:0.0000454, took: 23.9339s
Batch 1899/3125, reward: 0.133, revenue_rate: 0.133, distance: 1.418, power: -0.2554, loss: -0.2219, lr:0.0000388, took: 25.4015s
Batch 1999/3125, reward: 0.132, revenue_rate: 0.132, distance: 1.406, power: -0.2568, loss: 0.0920, lr:0.0000322, took: 24.3001s
Batch 2099/3125, reward: 0.131, revenue_rate: 0.131, distance: 1.376, power: -0.2572, loss: -0.2178, lr:0.0000259, took: 26.2173s
Batch 2199/3125, reward: 0.131, revenue_rate: 0.131, distance: 1.380, power: -0.2583, loss: -0.0364, lr:0.0000200, took: 25.2351s
Batch 2299/3125, reward: 0.131, revenue_rate: 0.131, distance: 1.389, power: -0.2575, loss: 0.5463, lr:0.0000146, took: 23.9292s
Batch 2399/3125, reward: 0.131, revenue_rate: 0.131, distance: 1.389, power: -0.2561, loss: 0.2309, lr:0.0000100, took: 24.4668s
Batch 2499/3125, reward: 0.133, revenue_rate: 0.133, distance: 1.413, power: -0.2560, loss: 0.0446, lr:0.0000062, took: 25.4650s
Batch 2599/3125, reward: 0.135, revenue_rate: 0.135, distance: 1.458, power: -0.2545, loss: 0.1752, lr:0.0000034, took: 25.4785s
Batch 2699/3125, reward: 0.135, revenue_rate: 0.135, distance: 1.454, power: -0.2547, loss: -0.0731, lr:0.0000017, took: 25.4316s
Batch 2799/3125, reward: 0.137, revenue_rate: 0.137, distance: 1.474, power: -0.2540, loss: 0.0698, lr:0.0000010, took: 25.6385s
Batch 2899/3125, reward: 0.136, revenue_rate: 0.136, distance: 1.465, power: -0.2547, loss: -0.2308, lr:0.0000799, took: 25.2832s
Batch 2999/3125, reward: 0.133, revenue_rate: 0.133, distance: 1.432, power: -0.2524, loss: -0.2699, lr:0.0000795, took: 25.0327s
Batch 3099/3125, reward: 0.135, revenue_rate: 0.135, distance: 1.437, power: -0.2556, loss: -0.0323, lr:0.0000788, took: 25.0067s
Epoch 0 mean epoch loss/reward: -0.0170, 0.1187, -0.4739, took: 953.5876s (26.3274s / 100 batches)
Batch 99/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.355, power: -0.2538, loss: 0.8063, lr:0.0000776, took: 22.0136s
Batch 199/3125, reward: 0.131, revenue_rate: 0.131, distance: 1.399, power: -0.2564, loss: 0.7421, lr:0.0000764, took: 23.4207s
Batch 299/3125, reward: 0.129, revenue_rate: 0.129, distance: 1.357, power: -0.2571, loss: 0.4489, lr:0.0000749, took: 23.6674s
Batch 399/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.351, power: -0.2579, loss: 0.1949, lr:0.0000731, took: 23.9391s
Batch 499/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.347, power: -0.2578, loss: 0.2785, lr:0.0000712, took: 24.6665s
Batch 599/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.350, power: -0.2558, loss: 0.8424, lr:0.0000690, took: 23.5581s
Batch 699/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.332, power: -0.2585, loss: 0.7542, lr:0.0000666, took: 23.1863s
Batch 799/3125, reward: 0.116, revenue_rate: 0.116, distance: 1.393, power: -0.1749, loss: 0.2524, lr:0.0000640, took: 23.9985s
Batch 899/3125, reward: 0.121, revenue_rate: 0.121, distance: 1.416, power: -0.1949, loss: 0.0218, lr:0.0000613, took: 24.0576s
Batch 999/3125, reward: 0.124, revenue_rate: 0.124, distance: 1.417, power: -0.2065, loss: -0.4785, lr:0.0000584, took: 23.7443s
Batch 1099/3125, reward: 0.132, revenue_rate: 0.132, distance: 1.393, power: -0.2573, loss: -0.5053, lr:0.0000554, took: 24.6559s
Batch 1199/3125, reward: 0.134, revenue_rate: 0.134, distance: 1.438, power: -0.2552, loss: 0.4525, lr:0.0000522, took: 26.0068s
Batch 1299/3125, reward: 0.138, revenue_rate: 0.138, distance: 1.487, power: -0.2529, loss: 0.2142, lr:0.0000490, took: 27.3608s
Batch 1399/3125, reward: 0.134, revenue_rate: 0.134, distance: 1.421, power: -0.2555, loss: 0.1845, lr:0.0000458, took: 26.1278s
Batch 1499/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.347, power: -0.2594, loss: -0.3877, lr:0.0000425, took: 25.7285s
Batch 1599/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.336, power: -0.2578, loss: -0.6294, lr:0.0000392, took: 25.5383s
Batch 1699/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.349, power: -0.2589, loss: 0.2556, lr:0.0000359, took: 25.4937s
Batch 1799/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.348, power: -0.2585, loss: 0.0577, lr:0.0000326, took: 25.6862s
Batch 1899/3125, reward: 0.126, revenue_rate: 0.126, distance: 1.327, power: -0.2596, loss: -0.0185, lr:0.0000294, took: 24.6747s
Batch 1999/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.333, power: -0.2584, loss: 0.1156, lr:0.0000263, took: 24.7882s
Batch 2099/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.334, power: -0.2578, loss: 0.3502, lr:0.0000232, took: 24.9063s
Batch 2199/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.329, power: -0.2590, loss: 0.1425, lr:0.0000203, took: 25.1006s
Batch 2299/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.333, power: -0.2593, loss: -0.0591, lr:0.0000175, took: 24.5657s
Batch 2399/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.342, power: -0.2579, loss: 0.1740, lr:0.0000149, took: 24.6411s
Batch 2499/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.340, power: -0.2582, loss: 0.2136, lr:0.0000125, took: 24.9786s
Batch 2599/3125, reward: 0.126, revenue_rate: 0.126, distance: 1.328, power: -0.2582, loss: -0.0140, lr:0.0000103, took: 24.3423s
Batch 2699/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.329, power: -0.2583, loss: 0.2199, lr:0.0000082, took: 24.6279s
Batch 2799/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.338, power: -0.2578, loss: 0.2207, lr:0.0000064, took: 24.3475s
Batch 2899/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.321, power: -0.2594, loss: 0.1439, lr:0.0000049, took: 23.5985s
Batch 2999/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.343, power: -0.2581, loss: 0.0786, lr:0.0000036, took: 24.2959s
Batch 3099/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.329, power: -0.2591, loss: 0.1354, lr:0.0000025, took: 23.8291s
Epoch 1 mean epoch loss/reward: 0.0747, 0.1233, -0.0818, took: 809.4987s (25.4467s / 100 batches)
Batch 99/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.337, power: -0.2588, loss: 0.1495, lr:0.0000016, took: 26.7478s
Batch 199/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.336, power: -0.2588, loss: 0.0570, lr:0.0000012, took: 24.8772s
Batch 299/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.321, power: -0.2590, loss: 0.1478, lr:0.0000010, took: 23.5534s
Batch 399/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.335, power: -0.2580, loss: -0.0100, lr:0.0000800, took: 23.4275s
Batch 499/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.336, power: -0.2581, loss: 0.3787, lr:0.0000799, took: 23.0912s
Batch 599/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.337, power: -0.2577, loss: 0.2280, lr:0.0000797, took: 23.4490s
Batch 699/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.340, power: -0.2591, loss: 0.7625, lr:0.0000795, took: 23.4990s
Batch 799/3125, reward: 0.126, revenue_rate: 0.126, distance: 1.325, power: -0.2593, loss: 1.3947, lr:0.0000792, took: 23.2172s
Batch 899/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.321, power: -0.2588, loss: 1.2696, lr:0.0000788, took: 23.4465s
Batch 999/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.349, power: -0.2595, loss: 1.2793, lr:0.0000784, took: 24.2729s
Batch 1099/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.337, power: -0.2585, loss: 0.3348, lr:0.0000778, took: 24.2439s
Batch 1199/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.340, power: -0.2587, loss: 0.5378, lr:0.0000773, took: 23.9109s
Batch 1299/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.347, power: -0.2590, loss: -0.2426, lr:0.0000766, took: 24.4480s
Batch 1399/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.346, power: -0.2582, loss: 1.2206, lr:0.0000759, took: 23.2819s
Batch 1499/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.337, power: -0.2585, loss: 0.7254, lr:0.0000752, took: 29.8693s
Batch 1599/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.329, power: -0.2586, loss: 0.3485, lr:0.0000744, took: 24.3940s
Batch 1699/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.321, power: -0.2576, loss: -0.3102, lr:0.0000735, took: 24.9023s
Batch 1799/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.330, power: -0.2582, loss: 0.3422, lr:0.0000725, took: 26.3508s
Batch 1899/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.326, power: -0.2594, loss: 0.2639, lr:0.0000715, took: 26.2284s
Batch 1999/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.330, power: -0.2579, loss: 0.3356, lr:0.0000705, took: 68.4487s
Batch 2099/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.331, power: -0.2587, loss: -0.3306, lr:0.0000694, took: 32.9879s
Batch 2199/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.344, power: -0.2582, loss: 0.0594, lr:0.0000682, took: 24.6875s
Batch 2299/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.338, power: -0.2587, loss: -0.5250, lr:0.0000670, took: 24.3645s
Batch 2399/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.348, power: -0.2584, loss: -0.4422, lr:0.0000658, took: 23.9066s
Batch 2499/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.328, power: -0.2589, loss: 0.1327, lr:0.0000645, took: 23.8510s
Batch 2599/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.341, power: -0.2578, loss: 0.0895, lr:0.0000631, took: 24.9148s
Batch 2699/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.336, power: -0.2588, loss: -0.1727, lr:0.0000618, took: 24.3866s
Batch 2799/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.330, power: -0.2577, loss: -0.2271, lr:0.0000604, took: 24.1867s
Batch 2899/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.322, power: -0.2574, loss: 0.1949, lr:0.0000589, took: 24.8064s
Batch 2999/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.333, power: -0.2586, loss: 0.1516, lr:0.0000574, took: 24.5733s
Batch 3099/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.331, power: -0.2593, loss: 0.3834, lr:0.0000559, took: 27.0368s
Epoch 2 mean epoch loss/reward: 0.1418, 0.1246, -0.0797, took: 870.8255s (25.7318s / 100 batches)
