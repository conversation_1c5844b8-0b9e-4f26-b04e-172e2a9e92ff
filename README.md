# SMP模型优化

本项目对基于GPN和IndRNN的卫星任务规划(SMP)模型进行了一系列优化，旨在提高reward指标。

## 主要改进

### 1. 注意力机制优化
- 实现了更高效的`MultiHead_Additive_Attention`类
- 添加了层归一化和残差连接
- 使用了更好的参数初始化方法（Kaiming初始化）
- 添加了注意力dropout以防止过拟合

### 2. IndRNN网络优化
- 改进了`IndRNN_Net`类，添加了输入输出投影层
- 添加了更有效的残差连接机制
- 增加了层归一化以提高训练稳定性
- 优化了参数初始化方法

### 3. 超参数优化
- 增大了梯度裁剪阈值（从1.0到2.0）
- 略微增加了dropout率（从0.1到0.15）
- 增大了学习率（actor_lr和critic_lr从5e-5到8e-5）
- 增大了批量大小（从8到16）
- 增加了网络层数（从1层到2层）

### 4. 学习率调度优化
- 使用余弦退火学习率调度器替代原有的StepLR
- 实现了学习率周期性重启策略，有助于跳出局部最优
- 为actor和critic分别设置了学习率调度器

## 预期效果
这些优化预计将带来以下改进：
1. 更快的收敛速度
2. 更高的reward值
3. 更好的泛化能力
4. 更稳定的训练过程

## 使用方法
保持原有的训练命令不变，优化已经集成到代码中。

# 卫星任务规划问题 (SMP) 求解器

## 注意力机制消融实验

为了评估多头注意力机制在卫星任务规划问题中的有效性，我们实现了以下几种注意力机制变体：

1. **标准多头注意力 (multi_head)**: 原始实现的多头注意力机制，默认使用8个头。
2. **单头注意力 (single_head)**: 只使用一个注意力头的简化版本。
3. **无层归一化多头注意力 (no_layer_norm)**: 移除了层归一化的多头注意力。
4. **无缩放因子多头注意力 (no_scale)**: 移除了缩放因子的多头注意力。
5. **可配置头数多头注意力 (configurable)**: 可以配置不同头数的多头注意力，用于测试头数对性能的影响。

### 运行消融实验

使用以下命令运行所有消融实验：

```bash
python run_ablation.py --batch_size 8 --epochs 3 --nodes 100 --seed 12345
```

参数说明：
- `--batch_size`: 批量大小
- `--epochs`: 训练轮数
- `--nodes`: 节点数量
- `--seed`: 随机种子

### 分析实验结果

使用以下命令分析和可视化实验结果：

```bash
python analyze_ablation.py --task single_smp --nodes 100
```

这将生成：
1. `ablation_results.png`: 包含不同注意力机制性能对比和头数影响的图表
2. `ablation_results.csv`: 包含各种注意力机制平均性能的CSV文件

### 单独测试特定注意力机制

可以使用以下命令测试特定的注意力机制：

```bash
python train.py --model gpn --rnn indrnn --attention_type multi_head --n_head 8
```

可用的注意力类型：
- `multi_head`: 标准多头注意力
- `single_head`: 单头注意力
- `no_layer_norm`: 无层归一化多头注意力
- `no_scale`: 无缩放因子多头注意力
- `configurable`: 可配置头数的多头注意力 