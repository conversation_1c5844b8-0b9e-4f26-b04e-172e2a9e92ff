import argparse

parser = argparse.ArgumentParser(description='Combinatorial Optimization')
# model
# pn
# gpn
parser.add_argument('--model', default='gpn')
# train
parser.add_argument('--task', default='single_smp')
parser.add_argument('--seed', default=12346, type=int)  # 12345
parser.add_argument('--checkpoint', default=None)
parser.add_argument('--test', action='store_true', default=False)
parser.add_argument('--max_grad_norm', default=1.0, type=float)
parser.add_argument('--dropout', default=0.15, type=float)
parser.add_argument('--actor_lr', default=8e-5, type=float)
parser.add_argument('--critic_lr', default=8e-5, type=float)
parser.add_argument('--train_size', default=100000, type=int)
parser.add_argument('--valid_size', default=10000, type=int)
parser.add_argument('--epochs', default=3, type=int)
parser.add_argument('--lr', type=float, default=2e-4, help="learning rate")
parser.add_argument('--nodes', dest='num_nodes', default=100, type=int)
parser.add_argument('--hidden', dest='hidden_size', default=256, type=int)
parser.add_argument('--batch_size', default=32, type=int)
parser.add_argument('--static_size', default=8, type=int)
parser.add_argument('--dynamic_size', default=6, type=int)

parser.add_argument('--memory_total', default=0.3, type=float)
parser.add_argument('--power_total', default=5, type=float)

# MultiHead_Additive_Attention
parser.add_argument('--attention', default='MultiHead_Additive_Attention', type=str)
parser.add_argument('--n_head', default=1, type=int)

# 注意力机制类型，用于消融实验
# multi_head: 标准多头注意力机制
# single_head: 单头注意力机制
# no_layer_norm: 无层归一化的多头注意力
# no_scale: 无缩放因子的多头注意力
# configurable: 可配置头数的多头注意力
parser.add_argument('--attention_type', default='single_head', type=str, 
                    choices=['multi_head', 'single_head', 'no_layer_norm', 'no_scale', 'configurable'],
                    help='Attention mechanism type for ablation study')

# lstm
# indrnn
# indrnnv2
parser.add_argument('--rnn', default='indrnn', type=str)
parser.add_argument('--layers', dest='num_layers', default=2, type=int)

# conv1d
parser.add_argument('--encoder', default='conv1d', type=str)

args = parser.parse_args()
