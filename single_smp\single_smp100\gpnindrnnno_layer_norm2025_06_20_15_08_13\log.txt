single_smp: 100
model: gpn
rnn: indrnn
hidden_size: 256
batch_size: 32
seed: 12345
train-size: 100000
valid-size: 10000
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 8e-05
critic_lr: 8e-05
attention_type: no_layer_norm
n_head: 8
2025_06_20_15_08_13
Batch 99/3125, reward: 0.080, revenue_rate: 0.080, distance: 1.387, power: 0.0211, loss: -0.4453, lr:0.0000778, took: 24.0426s
Batch 199/3125, reward: 0.084, revenue_rate: 0.084, distance: 1.413, power: 0.0189, loss: -0.6207, lr:0.0000714, took: 25.0095s
Batch 299/3125, reward: 0.088, revenue_rate: 0.088, distance: 1.449, power: 0.0133, loss: -0.6321, lr:0.0000616, took: 24.4139s
Batch 399/3125, reward: 0.097, revenue_rate: 0.097, distance: 1.528, power: 0.0012, loss: 0.7449, lr:0.0000495, took: 25.3457s
Batch 499/3125, reward: 0.112, revenue_rate: 0.112, distance: 1.615, power: -0.0544, loss: -1.1323, lr:0.0000363, took: 26.8349s
Batch 599/3125, reward: 0.130, revenue_rate: 0.130, distance: 1.569, power: -0.1766, loss: -0.1103, lr:0.0000236, took: 28.5974s
Batch 699/3125, reward: 0.134, revenue_rate: 0.134, distance: 1.557, power: -0.2029, loss: -0.1937, lr:0.0000128, took: 26.9402s
Batch 799/3125, reward: 0.134, revenue_rate: 0.134, distance: 1.529, power: -0.2194, loss: 0.2145, lr:0.0000051, took: 27.4837s
Batch 899/3125, reward: 0.134, revenue_rate: 0.134, distance: 1.504, power: -0.2276, loss: 0.0418, lr:0.0000013, took: 28.7423s
Batch 999/3125, reward: 0.134, revenue_rate: 0.134, distance: 1.509, power: -0.2250, loss: 0.0569, lr:0.0000798, took: 26.5966s
Batch 1099/3125, reward: 0.112, revenue_rate: 0.112, distance: 1.668, power: -0.0423, loss: -0.4999, lr:0.0000785, took: 27.8403s
Batch 1199/3125, reward: 0.120, revenue_rate: 0.120, distance: 1.738, power: -0.0580, loss: 0.5900, lr:0.0000762, took: 32.1851s
Batch 1299/3125, reward: 0.134, revenue_rate: 0.134, distance: 1.669, power: -0.1577, loss: 0.6760, lr:0.0000729, took: 33.0266s
Batch 1399/3125, reward: 0.140, revenue_rate: 0.140, distance: 1.609, power: -0.2230, loss: 0.5318, lr:0.0000687, took: 48.9732s
Batch 1499/3125, reward: 0.139, revenue_rate: 0.139, distance: 1.571, power: -0.2313, loss: 0.6108, lr:0.0000637, took: 44.8067s
Batch 1599/3125, reward: 0.140, revenue_rate: 0.140, distance: 1.549, power: -0.2417, loss: -0.3608, lr:0.0000580, took: 27.5313s
Batch 1699/3125, reward: 0.139, revenue_rate: 0.139, distance: 1.503, power: -0.2513, loss: -0.1623, lr:0.0000519, took: 26.6569s
Batch 1799/3125, reward: 0.139, revenue_rate: 0.139, distance: 1.524, power: -0.2520, loss: -0.0583, lr:0.0000454, took: 27.2515s
Batch 1899/3125, reward: 0.140, revenue_rate: 0.140, distance: 1.549, power: -0.2512, loss: -0.3026, lr:0.0000388, took: 27.6107s
Batch 1999/3125, reward: 0.142, revenue_rate: 0.142, distance: 1.560, power: -0.2502, loss: -0.0801, lr:0.0000322, took: 28.3966s
Batch 2099/3125, reward: 0.143, revenue_rate: 0.143, distance: 1.552, power: -0.2511, loss: -0.2168, lr:0.0000259, took: 29.2474s
Batch 2199/3125, reward: 0.145, revenue_rate: 0.145, distance: 1.599, power: -0.2510, loss: 0.1096, lr:0.0000200, took: 29.9320s
Batch 2299/3125, reward: 0.147, revenue_rate: 0.147, distance: 1.626, power: -0.2501, loss: 0.0777, lr:0.0000146, took: 32.0889s
Batch 2399/3125, reward: 0.149, revenue_rate: 0.149, distance: 1.674, power: -0.2444, loss: -0.1002, lr:0.0000100, took: 30.8990s
Batch 2499/3125, reward: 0.154, revenue_rate: 0.154, distance: 1.777, power: -0.2297, loss: 0.2644, lr:0.0000062, took: 32.6899s
Batch 2599/3125, reward: 0.154, revenue_rate: 0.154, distance: 1.801, power: -0.2282, loss: 0.0811, lr:0.0000034, took: 32.1332s
Batch 2699/3125, reward: 0.156, revenue_rate: 0.156, distance: 1.822, power: -0.2228, loss: 0.0822, lr:0.0000017, took: 33.2847s
Batch 2799/3125, reward: 0.157, revenue_rate: 0.157, distance: 1.838, power: -0.2248, loss: 0.1031, lr:0.0000010, took: 33.2746s
Batch 2899/3125, reward: 0.158, revenue_rate: 0.158, distance: 1.940, power: -0.1874, loss: 0.1751, lr:0.0000799, took: 33.8106s
Batch 2999/3125, reward: 0.176, revenue_rate: 0.176, distance: 2.260, power: -0.1625, loss: 1.2262, lr:0.0000795, took: 37.6593s
Batch 3099/3125, reward: 0.197, revenue_rate: 0.197, distance: 2.680, power: -0.1158, loss: -0.2792, lr:0.0000788, took: 43.1353s
Epoch 0 mean epoch loss/reward: 0.0309, 0.1364, -0.7833, took: 1160.2268s (30.8529s / 100 batches)
Batch 99/3125, reward: 0.264, revenue_rate: 0.264, distance: 3.637, power: -0.1049, loss: 0.4382, lr:0.0000776, took: 54.6051s
Batch 199/3125, reward: 0.346, revenue_rate: 0.346, distance: 4.754, power: -0.1038, loss: -0.4247, lr:0.0000764, took: 71.6028s
Batch 299/3125, reward: 0.370, revenue_rate: 0.370, distance: 5.088, power: -0.0970, loss: 1.0372, lr:0.0000749, took: 75.1962s
Batch 399/3125, reward: 0.416, revenue_rate: 0.416, distance: 5.661, power: -0.1151, loss: 1.1058, lr:0.0000731, took: 94.0107s
Batch 499/3125, reward: 0.395, revenue_rate: 0.395, distance: 5.471, power: -0.0952, loss: 1.6648, lr:0.0000712, took: 114.3241s
Batch 599/3125, reward: 0.483, revenue_rate: 0.483, distance: 6.757, power: -0.0779, loss: -2.5973, lr:0.0000690, took: 206.9786s
Batch 699/3125, reward: 0.488, revenue_rate: 0.488, distance: 6.929, power: -0.0595, loss: -2.4538, lr:0.0000666, took: 224.0328s
Batch 799/3125, reward: 0.488, revenue_rate: 0.488, distance: 6.977, power: -0.0485, loss: -1.8302, lr:0.0000640, took: 233.1902s
Batch 899/3125, reward: 0.555, revenue_rate: 0.555, distance: 8.012, power: -0.0318, loss: -1.0129, lr:0.0000613, took: 295.6866s
Batch 999/3125, reward: 0.577, revenue_rate: 0.577, distance: 8.416, power: -0.0196, loss: 0.4227, lr:0.0000584, took: 314.9195s
Batch 1099/3125, reward: 0.629, revenue_rate: 0.629, distance: 9.304, power: 0.0016, loss: 2.1149, lr:0.0000554, took: 364.3124s
Batch 1199/3125, reward: 0.671, revenue_rate: 0.671, distance: 9.983, power: 0.0230, loss: 2.1760, lr:0.0000522, took: 415.0362s
Batch 1299/3125, reward: 0.626, revenue_rate: 0.626, distance: 9.417, power: 0.0289, loss: -0.3415, lr:0.0000490, took: 394.2538s
Batch 1399/3125, reward: 0.704, revenue_rate: 0.704, distance: 10.565, power: 0.0394, loss: 1.8988, lr:0.0000458, took: 453.8243s
Batch 1499/3125, reward: 0.723, revenue_rate: 0.723, distance: 10.899, power: 0.0495, loss: -0.9785, lr:0.0000425, took: 477.3193s
Batch 1599/3125, reward: 0.715, revenue_rate: 0.715, distance: 10.833, power: 0.0535, loss: 0.8921, lr:0.0000392, took: 457.3094s
Batch 1699/3125, reward: 0.750, revenue_rate: 0.750, distance: 11.355, power: 0.0563, loss: 1.7374, lr:0.0000359, took: 499.8365s
Batch 1799/3125, reward: 0.729, revenue_rate: 0.729, distance: 11.092, power: 0.0575, loss: 0.5727, lr:0.0000326, took: 477.4857s
Batch 1899/3125, reward: 0.758, revenue_rate: 0.758, distance: 11.563, power: 0.0629, loss: 0.2222, lr:0.0000294, took: 500.6971s
Batch 1999/3125, reward: 0.769, revenue_rate: 0.769, distance: 11.745, power: 0.0699, loss: 0.6477, lr:0.0000263, took: 517.4659s
Batch 2099/3125, reward: 0.771, revenue_rate: 0.771, distance: 11.802, power: 0.0729, loss: 0.1404, lr:0.0000232, took: 534.1269s
Batch 2199/3125, reward: 0.787, revenue_rate: 0.787, distance: 12.055, power: 0.0764, loss: 0.7913, lr:0.0000203, took: 543.5497s
Batch 2299/3125, reward: 0.789, revenue_rate: 0.789, distance: 12.121, power: 0.0776, loss: -0.7588, lr:0.0000175, took: 550.2623s
Batch 2399/3125, reward: 0.790, revenue_rate: 0.790, distance: 12.112, power: 0.0802, loss: -0.0577, lr:0.0000149, took: 552.3922s
Batch 2499/3125, reward: 0.791, revenue_rate: 0.791, distance: 12.138, power: 0.0824, loss: -0.6823, lr:0.0000125, took: 552.4651s
Batch 2599/3125, reward: 0.796, revenue_rate: 0.796, distance: 12.212, power: 0.0824, loss: 0.5371, lr:0.0000103, took: 555.5574s
Batch 2699/3125, reward: 0.798, revenue_rate: 0.798, distance: 12.280, power: 0.0840, loss: 0.0446, lr:0.0000082, took: 555.0944s
Batch 2799/3125, reward: 0.801, revenue_rate: 0.801, distance: 12.310, power: 0.0839, loss: -0.0380, lr:0.0000064, took: 560.8161s
Batch 2899/3125, reward: 0.800, revenue_rate: 0.800, distance: 12.287, power: 0.0840, loss: 0.6686, lr:0.0000049, took: 563.4796s
Batch 2999/3125, reward: 0.801, revenue_rate: 0.801, distance: 12.326, power: 0.0842, loss: -0.1014, lr:0.0000036, took: 562.6335s
Batch 3099/3125, reward: 0.804, revenue_rate: 0.804, distance: 12.348, power: 0.0848, loss: 1.0920, lr:0.0000025, took: 556.8124s
Epoch 1 mean epoch loss/reward: 0.1254, 0.3944, -0.8702, took: 12688.7833s (214.2858s / 100 batches)
Batch 99/3125, reward: 0.804, revenue_rate: 0.804, distance: 12.368, power: 0.0842, loss: 0.7576, lr:0.0000016, took: 566.2981s
Batch 199/3125, reward: 0.806, revenue_rate: 0.806, distance: 12.397, power: 0.0839, loss: -0.0774, lr:0.0000012, took: 561.3058s
Batch 299/3125, reward: 0.804, revenue_rate: 0.804, distance: 12.375, power: 0.0831, loss: 0.1722, lr:0.0000010, took: 565.7832s
Batch 399/3125, reward: 0.711, revenue_rate: 0.711, distance: 10.878, power: 0.0674, loss: -0.5647, lr:0.0000800, took: 502.9620s
Batch 499/3125, reward: 0.730, revenue_rate: 0.730, distance: 11.157, power: 0.0694, loss: 3.0640, lr:0.0000799, took: 474.8067s
Batch 599/3125, reward: 0.683, revenue_rate: 0.683, distance: 10.374, power: 0.0605, loss: 3.6902, lr:0.0000797, took: 439.1038s
Batch 699/3125, reward: 0.723, revenue_rate: 0.723, distance: 10.840, power: 0.0479, loss: 1.5812, lr:0.0000795, took: 471.1701s
Batch 799/3125, reward: 0.740, revenue_rate: 0.740, distance: 11.001, power: 0.0453, loss: 3.2611, lr:0.0000792, took: 467.5752s
Batch 899/3125, reward: 0.751, revenue_rate: 0.751, distance: 11.297, power: 0.0561, loss: 2.0137, lr:0.0000788, took: 491.5490s
Batch 999/3125, reward: 0.760, revenue_rate: 0.760, distance: 11.412, power: 0.0574, loss: 0.0353, lr:0.0000784, took: 504.8256s
Batch 1099/3125, reward: 0.691, revenue_rate: 0.691, distance: 10.354, power: 0.0480, loss: -1.7590, lr:0.0000778, took: 467.1910s
Batch 1199/3125, reward: 0.751, revenue_rate: 0.751, distance: 11.308, power: 0.0597, loss: 0.0045, lr:0.0000773, took: 491.9698s
Batch 1299/3125, reward: 0.745, revenue_rate: 0.745, distance: 11.249, power: 0.0617, loss: 0.6977, lr:0.0000766, took: 500.3350s
Batch 1399/3125, reward: 0.743, revenue_rate: 0.743, distance: 11.240, power: 0.0635, loss: -0.0904, lr:0.0000759, took: 484.8497s
Batch 1499/3125, reward: 0.734, revenue_rate: 0.734, distance: 11.146, power: 0.0635, loss: -0.4913, lr:0.0000752, took: 477.5138s
Batch 1599/3125, reward: 0.760, revenue_rate: 0.760, distance: 11.485, power: 0.0657, loss: 0.7159, lr:0.0000744, took: 499.2801s
Batch 1699/3125, reward: 0.757, revenue_rate: 0.757, distance: 11.453, power: 0.0703, loss: -0.1417, lr:0.0000735, took: 512.6112s
Batch 1799/3125, reward: 0.745, revenue_rate: 0.745, distance: 11.361, power: 0.0733, loss: 1.9454, lr:0.0000725, took: 525.0529s
Batch 1899/3125, reward: 0.765, revenue_rate: 0.765, distance: 11.598, power: 0.0697, loss: -0.9865, lr:0.0000715, took: 538.5979s
Batch 1999/3125, reward: 0.754, revenue_rate: 0.754, distance: 11.466, power: 0.0701, loss: 0.5554, lr:0.0000705, took: 503.5724s
Batch 2099/3125, reward: 0.721, revenue_rate: 0.721, distance: 10.904, power: 0.0622, loss: 1.2618, lr:0.0000694, took: 472.8723s
Batch 2199/3125, reward: 0.753, revenue_rate: 0.753, distance: 11.317, power: 0.0612, loss: 1.7140, lr:0.0000682, took: 501.7449s
Batch 2299/3125, reward: 0.754, revenue_rate: 0.754, distance: 11.299, power: 0.0551, loss: 3.6957, lr:0.0000670, took: 481.7755s
Batch 2399/3125, reward: 0.780, revenue_rate: 0.780, distance: 11.800, power: 0.0678, loss: 3.1870, lr:0.0000658, took: 536.3734s
Batch 2499/3125, reward: 0.776, revenue_rate: 0.776, distance: 11.706, power: 0.0669, loss: -1.8699, lr:0.0000645, took: 527.9780s
Batch 2599/3125, reward: 0.785, revenue_rate: 0.785, distance: 11.854, power: 0.0678, loss: 1.0364, lr:0.0000631, took: 538.1052s
Batch 2699/3125, reward: 0.773, revenue_rate: 0.773, distance: 11.755, power: 0.0736, loss: 0.1120, lr:0.0000618, took: 526.3383s
Batch 2799/3125, reward: 0.733, revenue_rate: 0.733, distance: 11.134, power: 0.0690, loss: 3.2522, lr:0.0000604, took: 471.8655s
Batch 2899/3125, reward: 0.771, revenue_rate: 0.771, distance: 11.707, power: 0.0747, loss: 0.4626, lr:0.0000589, took: 524.7565s
Batch 2999/3125, reward: 0.776, revenue_rate: 0.776, distance: 11.829, power: 0.0787, loss: 0.6603, lr:0.0000574, took: 525.2903s
Batch 3099/3125, reward: 0.800, revenue_rate: 0.800, distance: 12.337, power: 0.0874, loss: 0.6652, lr:0.0000559, took: 548.7850s
Epoch 2 mean epoch loss/reward: 0.3903, 0.5142, -0.8702, took: 16040.9085s (311.6984s / 100 batches)
