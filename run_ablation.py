#!/usr/bin/env python
"""
消融实验脚本，用于测试不同的注意力机制变体
"""
import os
import subprocess
import argparse

# 解析命令行参数
parser = argparse.ArgumentParser(description='运行注意力机制消融实验')
parser.add_argument('--batch_size', type=int, default=32, help='批量大小')
parser.add_argument('--epochs', type=int, default=3, help='训练轮数')
parser.add_argument('--nodes', type=int, default=100, help='节点数量')
parser.add_argument('--seed', type=int, default=12345, help='随机种子')
args = parser.parse_args()

# 定义不同的消融实验配置
ablation_configs = [
    {
        'name': '标准多头注意力',
        'attention_type': 'multi_head',
        'n_head': 8,
    },
    {
        'name': '单头注意力',
        'attention_type': 'single_head',
        'n_head': 1,
    },
    # {
    #     'name': '无层归一化多头注意力',
    #     'attention_type': 'no_layer_norm',
    #     'n_head': 8,
    # },
    # {
    #     'name': '无缩放因子多头注意力',
    #     'attention_type': 'no_scale',
    #     'n_head': 8,
    # },
    # {
    #     'name': '2头注意力',
    #     'attention_type': 'configurable',
    #     'n_head': 2,
    # },
    # {
    #     'name': '4头注意力',
    #     'attention_type': 'configurable',
    #     'n_head': 4,
    # },
    # {
    #     'name': '16头注意力',
    #     'attention_type': 'configurable',
    #     'n_head': 16,
    # },
]

# 运行每个消融实验
print("开始运行消融实验...")
for config in ablation_configs:
    print(f"\n运行实验: {config['name']}")
    print(f"配置: attention_type={config['attention_type']}, n_head={config['n_head']}")
    
    # 构建命令
    cmd = [
        'python', 'train.py',
        '--model', 'gpn',
        '--rnn', 'indrnn',
        '--batch_size', str(args.batch_size),
        '--epochs', str(args.epochs),
        '--nodes', str(args.nodes),
        '--seed', str(args.seed),
        '--attention_type', config['attention_type'],
        '--n_head', str(config['n_head']),
    ]
    
    # 运行命令
    try:
        subprocess.run(cmd, check=True)
        print(f"实验 '{config['name']}' 完成")
    except subprocess.CalledProcessError as e:
        print(f"实验 '{config['name']}' 失败: {e}")

print("\n所有消融实验完成！") 