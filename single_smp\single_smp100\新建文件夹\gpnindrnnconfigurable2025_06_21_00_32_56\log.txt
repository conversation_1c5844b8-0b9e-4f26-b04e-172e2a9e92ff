single_smp: 100
model: gpn
rnn: indrnn
hidden_size: 256
batch_size: 32
seed: 12345
train-size: 100000
valid-size: 10000
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 8e-05
critic_lr: 8e-05
attention_type: configurable
n_head: 2
2025_06_21_00_32_56
Batch 99/3125, reward: 0.064, revenue_rate: 0.064, distance: 1.119, power: 0.0144, loss: 87.0377, lr:0.0000778, took: 19.7995s
Batch 199/3125, reward: 0.063, revenue_rate: 0.063, distance: 1.117, power: 0.0166, loss: -0.2218, lr:0.0000714, took: 19.6261s
Batch 299/3125, reward: 0.068, revenue_rate: 0.068, distance: 1.184, power: 0.0153, loss: -2.2002, lr:0.0000616, took: 20.3528s
Batch 399/3125, reward: 0.069, revenue_rate: 0.069, distance: 1.203, power: 0.0170, loss: 1.0685, lr:0.0000495, took: 20.3662s
Batch 499/3125, reward: 0.073, revenue_rate: 0.073, distance: 1.264, power: 0.0174, loss: 0.0589, lr:0.0000363, took: 21.1723s
Batch 599/3125, reward: 0.073, revenue_rate: 0.073, distance: 1.238, power: 0.0144, loss: -1.3159, lr:0.0000236, took: 21.4995s
Batch 699/3125, reward: 0.071, revenue_rate: 0.071, distance: 1.225, power: 0.0157, loss: -0.6500, lr:0.0000128, took: 20.9374s
Batch 799/3125, reward: 0.070, revenue_rate: 0.070, distance: 1.201, power: 0.0156, loss: 0.4015, lr:0.0000051, took: 20.6024s
Batch 899/3125, reward: 0.071, revenue_rate: 0.071, distance: 1.212, power: 0.0137, loss: 0.4365, lr:0.0000013, took: 20.6751s
Batch 999/3125, reward: 0.072, revenue_rate: 0.072, distance: 1.234, power: 0.0147, loss: 0.4579, lr:0.0000798, took: 21.1452s
Batch 1099/3125, reward: 0.091, revenue_rate: 0.091, distance: 1.439, power: -0.0064, loss: 3.4097, lr:0.0000785, took: 23.4773s
Batch 1199/3125, reward: 0.125, revenue_rate: 0.125, distance: 1.600, power: -0.1314, loss: 0.7165, lr:0.0000762, took: 26.0287s
Batch 1299/3125, reward: 0.119, revenue_rate: 0.119, distance: 1.675, power: -0.0773, loss: -0.5445, lr:0.0000729, took: 26.6425s
Batch 1399/3125, reward: 0.113, revenue_rate: 0.113, distance: 1.740, power: -0.0133, loss: 0.3995, lr:0.0000687, took: 27.7057s
Batch 1499/3125, reward: 0.126, revenue_rate: 0.126, distance: 1.866, power: -0.0463, loss: -4.3432, lr:0.0000637, took: 29.2055s
Batch 1599/3125, reward: 0.133, revenue_rate: 0.133, distance: 1.800, power: -0.1074, loss: 2.1162, lr:0.0000580, took: 28.8657s
Batch 1699/3125, reward: 0.135, revenue_rate: 0.135, distance: 1.666, power: -0.1694, loss: -1.7563, lr:0.0000519, took: 26.9818s
Batch 1799/3125, reward: 0.140, revenue_rate: 0.140, distance: 1.780, power: -0.1542, loss: -0.3706, lr:0.0000454, took: 28.8316s
Batch 1899/3125, reward: 0.146, revenue_rate: 0.146, distance: 1.963, power: -0.1218, loss: -0.2890, lr:0.0000388, took: 31.4761s
Batch 1999/3125, reward: 0.136, revenue_rate: 0.136, distance: 1.852, power: -0.1033, loss: 0.2379, lr:0.0000322, took: 29.2452s
Batch 2099/3125, reward: 0.136, revenue_rate: 0.136, distance: 1.866, power: -0.1018, loss: -0.4919, lr:0.0000259, took: 29.5165s
Batch 2199/3125, reward: 0.145, revenue_rate: 0.145, distance: 1.974, power: -0.1108, loss: -1.0562, lr:0.0000200, took: 31.0216s
Batch 2299/3125, reward: 0.148, revenue_rate: 0.148, distance: 1.988, power: -0.1165, loss: -1.4397, lr:0.0000146, took: 31.1555s
Batch 2399/3125, reward: 0.153, revenue_rate: 0.153, distance: 2.056, power: -0.1158, loss: 1.3203, lr:0.0000100, took: 33.1002s
Batch 2499/3125, reward: 0.152, revenue_rate: 0.152, distance: 2.035, power: -0.1207, loss: -0.4202, lr:0.0000062, took: 32.8482s
Batch 2599/3125, reward: 0.152, revenue_rate: 0.152, distance: 2.039, power: -0.1221, loss: -0.7847, lr:0.0000034, took: 33.1287s
Batch 2699/3125, reward: 0.154, revenue_rate: 0.154, distance: 2.071, power: -0.1218, loss: 1.3136, lr:0.0000017, took: 33.6747s
Batch 2799/3125, reward: 0.155, revenue_rate: 0.155, distance: 2.100, power: -0.1197, loss: 0.2506, lr:0.0000010, took: 34.3174s
Batch 2899/3125, reward: 0.146, revenue_rate: 0.146, distance: 1.931, power: -0.1359, loss: 4.8766, lr:0.0000799, took: 31.5467s
Batch 2999/3125, reward: 0.106, revenue_rate: 0.106, distance: 1.684, power: -0.0053, loss: -1.4515, lr:0.0000795, took: 27.6976s
Batch 3099/3125, reward: 0.090, revenue_rate: 0.090, distance: 1.511, power: 0.0090, loss: 1.8269, lr:0.0000788, took: 25.1486s
Epoch 0 mean epoch loss/reward: 2.8379, 0.1126, -0.7474, took: 1001.0087s (26.7030s / 100 batches)
Batch 99/3125, reward: 0.089, revenue_rate: 0.089, distance: 1.494, power: 0.0140, loss: -0.9173, lr:0.0000776, took: 23.6826s
Batch 199/3125, reward: 0.149, revenue_rate: 0.149, distance: 2.204, power: -0.0438, loss: 5.6935, lr:0.0000764, took: 34.8116s
Batch 299/3125, reward: 0.139, revenue_rate: 0.139, distance: 2.021, power: -0.0521, loss: -0.2749, lr:0.0000749, took: 32.4107s
Batch 399/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.976, power: -0.0196, loss: 0.5673, lr:0.0000731, took: 31.5431s
Batch 499/3125, reward: 0.112, revenue_rate: 0.112, distance: 1.797, power: -0.0057, loss: -0.5953, lr:0.0000712, took: 29.6453s
Batch 599/3125, reward: 0.175, revenue_rate: 0.175, distance: 2.562, power: -0.0531, loss: 1.6059, lr:0.0000690, took: 41.4962s
Batch 699/3125, reward: 0.189, revenue_rate: 0.189, distance: 2.730, power: -0.0696, loss: 0.4725, lr:0.0000666, took: 42.6897s
Batch 799/3125, reward: 0.198, revenue_rate: 0.198, distance: 2.813, power: -0.0794, loss: 1.7045, lr:0.0000640, took: 44.8099s
Batch 899/3125, reward: 0.094, revenue_rate: 0.094, distance: 1.549, power: 0.0037, loss: 0.1361, lr:0.0000613, took: 25.7376s
Batch 999/3125, reward: 0.100, revenue_rate: 0.100, distance: 1.607, power: -0.0027, loss: 2.5218, lr:0.0000584, took: 26.5618s
Batch 1099/3125, reward: 0.114, revenue_rate: 0.114, distance: 1.804, power: -0.0096, loss: 0.9112, lr:0.0000554, took: 28.8279s
Batch 1199/3125, reward: 0.099, revenue_rate: 0.099, distance: 1.624, power: 0.0072, loss: -1.1372, lr:0.0000522, took: 26.7612s
Batch 1299/3125, reward: 0.106, revenue_rate: 0.106, distance: 1.719, power: 0.0017, loss: -0.5094, lr:0.0000490, took: 28.1474s
Batch 1399/3125, reward: 0.103, revenue_rate: 0.103, distance: 1.672, power: 0.0036, loss: -0.1881, lr:0.0000458, took: 27.9815s
Batch 1499/3125, reward: 0.093, revenue_rate: 0.093, distance: 1.545, power: 0.0103, loss: -1.5977, lr:0.0000425, took: 26.1673s
Batch 1599/3125, reward: 0.086, revenue_rate: 0.086, distance: 1.474, power: 0.0126, loss: 0.2958, lr:0.0000392, took: 23.8417s
Batch 1699/3125, reward: 0.085, revenue_rate: 0.085, distance: 1.454, power: 0.0136, loss: 1.1031, lr:0.0000359, took: 23.9355s
Batch 1799/3125, reward: 0.085, revenue_rate: 0.085, distance: 1.455, power: 0.0161, loss: 0.2326, lr:0.0000326, took: 24.0659s
Batch 1899/3125, reward: 0.087, revenue_rate: 0.087, distance: 1.479, power: 0.0165, loss: -0.0445, lr:0.0000294, took: 24.2979s
Batch 1999/3125, reward: 0.091, revenue_rate: 0.091, distance: 1.526, power: 0.0117, loss: 1.4646, lr:0.0000263, took: 24.9003s
Batch 2099/3125, reward: 0.090, revenue_rate: 0.090, distance: 1.527, power: 0.0159, loss: -0.4634, lr:0.0000232, took: 25.1805s
Batch 2199/3125, reward: 0.092, revenue_rate: 0.092, distance: 1.554, power: 0.0124, loss: 0.6854, lr:0.0000203, took: 25.5650s
Batch 2299/3125, reward: 0.091, revenue_rate: 0.091, distance: 1.540, power: 0.0134, loss: -0.0719, lr:0.0000175, took: 25.3821s
Batch 2399/3125, reward: 0.093, revenue_rate: 0.093, distance: 1.562, power: 0.0135, loss: -0.2381, lr:0.0000149, took: 25.3986s
Batch 2499/3125, reward: 0.092, revenue_rate: 0.092, distance: 1.542, power: 0.0129, loss: 0.6188, lr:0.0000125, took: 25.5714s
Batch 2599/3125, reward: 0.093, revenue_rate: 0.093, distance: 1.555, power: 0.0123, loss: -0.9367, lr:0.0000103, took: 25.1387s
Batch 2699/3125, reward: 0.098, revenue_rate: 0.098, distance: 1.630, power: 0.0164, loss: 0.7259, lr:0.0000082, took: 26.5242s
Batch 2799/3125, reward: 0.109, revenue_rate: 0.109, distance: 1.780, power: 0.0085, loss: 0.7681, lr:0.0000064, took: 29.0921s
Batch 2899/3125, reward: 0.149, revenue_rate: 0.149, distance: 2.308, power: -0.0123, loss: 0.1364, lr:0.0000049, took: 35.7845s
Batch 2999/3125, reward: 0.177, revenue_rate: 0.177, distance: 2.642, power: -0.0293, loss: 0.8202, lr:0.0000036, took: 40.0655s
Batch 3099/3125, reward: 0.201, revenue_rate: 0.201, distance: 2.956, power: -0.0486, loss: 0.9770, lr:0.0000025, took: 43.6141s
Epoch 1 mean epoch loss/reward: 1.6566, 0.1148, -0.7760, took: 1115.7377s (28.1843s / 100 batches)
Batch 99/3125, reward: 0.220, revenue_rate: 0.220, distance: 3.149, power: -0.0660, loss: -0.0306, lr:0.0000016, took: 45.7885s
Batch 199/3125, reward: 0.225, revenue_rate: 0.225, distance: 3.202, power: -0.0742, loss: 0.5238, lr:0.0000012, took: 47.6418s
Batch 299/3125, reward: 0.227, revenue_rate: 0.227, distance: 3.214, power: -0.0817, loss: 0.3901, lr:0.0000010, took: 47.7622s
Batch 399/3125, reward: 0.207, revenue_rate: 0.207, distance: 2.992, power: -0.0563, loss: 2.8429, lr:0.0000800, took: 44.5481s
Batch 499/3125, reward: 0.216, revenue_rate: 0.216, distance: 3.149, power: -0.0602, loss: -1.9838, lr:0.0000799, took: 46.2017s
Batch 599/3125, reward: 0.124, revenue_rate: 0.124, distance: 1.871, power: -0.0329, loss: -0.7689, lr:0.0000797, took: 31.0726s
Batch 699/3125, reward: 0.217, revenue_rate: 0.217, distance: 3.087, power: -0.0746, loss: 1.4530, lr:0.0000795, took: 46.1071s
Batch 799/3125, reward: 0.253, revenue_rate: 0.253, distance: 3.574, power: -0.0892, loss: -3.4089, lr:0.0000792, took: 52.4402s
Batch 899/3125, reward: 0.248, revenue_rate: 0.248, distance: 3.499, power: -0.0907, loss: 0.6255, lr:0.0000788, took: 52.5021s
Batch 999/3125, reward: 0.268, revenue_rate: 0.268, distance: 3.792, power: -0.0854, loss: -0.0479, lr:0.0000784, took: 55.0445s
Batch 1099/3125, reward: 0.238, revenue_rate: 0.238, distance: 3.426, power: -0.0712, loss: -1.4906, lr:0.0000778, took: 52.8216s
Batch 1199/3125, reward: 0.094, revenue_rate: 0.094, distance: 1.528, power: -0.0000, loss: -2.1025, lr:0.0000773, took: 25.6631s
Batch 1299/3125, reward: 0.094, revenue_rate: 0.094, distance: 1.548, power: 0.0027, loss: 0.1997, lr:0.0000766, took: 25.4828s
Batch 1399/3125, reward: 0.111, revenue_rate: 0.111, distance: 1.702, power: -0.0242, loss: 0.9022, lr:0.0000759, took: 29.1075s
Batch 1499/3125, reward: 0.247, revenue_rate: 0.247, distance: 3.459, power: -0.0873, loss: 3.2267, lr:0.0000752, took: 52.8961s
Batch 1599/3125, reward: 0.131, revenue_rate: 0.131, distance: 1.979, power: -0.0300, loss: 0.3965, lr:0.0000744, took: 33.1628s
Batch 1699/3125, reward: 0.149, revenue_rate: 0.149, distance: 2.181, power: -0.0387, loss: 2.0929, lr:0.0000735, took: 36.2775s
Batch 1799/3125, reward: 0.256, revenue_rate: 0.256, distance: 3.571, power: -0.0860, loss: -0.3137, lr:0.0000725, took: 55.3853s
Batch 1899/3125, reward: 0.119, revenue_rate: 0.119, distance: 1.888, power: 0.0025, loss: 5.6225, lr:0.0000715, took: 29.4036s
Batch 1999/3125, reward: 0.151, revenue_rate: 0.151, distance: 2.257, power: -0.0277, loss: -0.7382, lr:0.0000705, took: 36.2349s
Batch 2099/3125, reward: 0.195, revenue_rate: 0.195, distance: 2.781, power: -0.0614, loss: 0.5536, lr:0.0000694, took: 44.5849s
Batch 2199/3125, reward: 0.272, revenue_rate: 0.272, distance: 3.796, power: -0.0815, loss: 2.2363, lr:0.0000682, took: 57.3902s
Batch 2299/3125, reward: 0.250, revenue_rate: 0.250, distance: 3.458, power: -0.0869, loss: 1.6483, lr:0.0000670, took: 56.2522s
Batch 2399/3125, reward: 0.259, revenue_rate: 0.259, distance: 3.642, power: -0.0715, loss: -0.4385, lr:0.0000658, took: 62.5742s
Batch 2499/3125, reward: 0.288, revenue_rate: 0.288, distance: 4.035, power: -0.0801, loss: 2.5675, lr:0.0000645, took: 64.0548s
Batch 2599/3125, reward: 0.301, revenue_rate: 0.301, distance: 4.247, power: -0.0689, loss: 1.4925, lr:0.0000631, took: 62.1602s
Batch 2699/3125, reward: 0.294, revenue_rate: 0.294, distance: 4.117, power: -0.0867, loss: 4.2564, lr:0.0000618, took: 68.2821s
Batch 2799/3125, reward: 0.312, revenue_rate: 0.312, distance: 4.469, power: -0.0617, loss: 1.0167, lr:0.0000604, took: 64.3767s
Batch 2899/3125, reward: 0.329, revenue_rate: 0.329, distance: 4.678, power: -0.0612, loss: 0.5589, lr:0.0000589, took: 69.2206s
Batch 2999/3125, reward: 0.339, revenue_rate: 0.339, distance: 4.803, power: -0.0649, loss: 1.6295, lr:0.0000574, took: 76.3005s
Batch 3099/3125, reward: 0.348, revenue_rate: 0.348, distance: 4.973, power: -0.0607, loss: -0.6075, lr:0.0000559, took: 75.8642s
Epoch 2 mean epoch loss/reward: 1.3326, 0.1519, -0.8170, took: 1764.3551s (35.4197s / 100 batches)
