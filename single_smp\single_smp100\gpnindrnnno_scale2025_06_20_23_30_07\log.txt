single_smp: 100
model: gpn
rnn: indrnn
hidden_size: 256
batch_size: 32
seed: 12345
train-size: 100000
valid-size: 10000
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 8e-05
critic_lr: 8e-05
attention_type: no_scale
n_head: 8
2025_06_20_23_30_07
Batch 99/3125, reward: 0.064, revenue_rate: 0.064, distance: 1.115, power: 0.0140, loss: 72.1143, lr:0.0000778, took: 20.5677s
Batch 199/3125, reward: 0.059, revenue_rate: 0.059, distance: 1.035, power: 0.0135, loss: 0.4474, lr:0.0000714, took: 18.7289s
Batch 299/3125, reward: 0.057, revenue_rate: 0.057, distance: 1.009, power: 0.0116, loss: 0.2239, lr:0.0000616, took: 18.3539s
<PERSON>ch 399/3125, reward: 0.063, revenue_rate: 0.063, distance: 1.082, power: 0.0002, loss: -0.4550, lr:0.0000495, took: 19.4609s
Batch 499/3125, reward: 0.113, revenue_rate: 0.113, distance: 1.297, power: -0.1930, loss: -0.1355, lr:0.0000363, took: 22.0468s
Batch 599/3125, reward: 0.125, revenue_rate: 0.125, distance: 1.331, power: -0.2458, loss: 0.9997, lr:0.0000236, took: 22.1773s
Batch 699/3125, reward: 0.123, revenue_rate: 0.123, distance: 1.304, power: -0.2380, loss: 0.5171, lr:0.0000128, took: 22.2427s
Batch 799/3125, reward: 0.126, revenue_rate: 0.126, distance: 1.341, power: -0.2464, loss: -0.4512, lr:0.0000051, took: 22.4477s
Batch 899/3125, reward: 0.125, revenue_rate: 0.125, distance: 1.333, power: -0.2452, loss: 0.3760, lr:0.0000013, took: 22.5922s
Batch 999/3125, reward: 0.126, revenue_rate: 0.126, distance: 1.350, power: -0.2505, loss: -0.0681, lr:0.0000798, took: 22.6042s
Batch 1099/3125, reward: 0.099, revenue_rate: 0.099, distance: 1.359, power: -0.0901, loss: 2.6591, lr:0.0000785, took: 22.6223s
Batch 1199/3125, reward: 0.086, revenue_rate: 0.086, distance: 1.411, power: -0.0017, loss: -2.0876, lr:0.0000762, took: 22.7663s
Batch 1299/3125, reward: 0.115, revenue_rate: 0.115, distance: 1.454, power: -0.1459, loss: 0.1140, lr:0.0000729, took: 23.3277s
Batch 1399/3125, reward: 0.125, revenue_rate: 0.125, distance: 1.436, power: -0.2070, loss: -1.0932, lr:0.0000687, took: 23.4752s
Batch 1499/3125, reward: 0.129, revenue_rate: 0.129, distance: 1.366, power: -0.2567, loss: 0.3053, lr:0.0000637, took: 22.8864s
Batch 1599/3125, reward: 0.130, revenue_rate: 0.130, distance: 1.387, power: -0.2525, loss: 0.0608, lr:0.0000580, took: 23.2536s
Batch 1699/3125, reward: 0.130, revenue_rate: 0.130, distance: 1.385, power: -0.2549, loss: -0.4366, lr:0.0000519, took: 23.5389s
Batch 1799/3125, reward: 0.130, revenue_rate: 0.130, distance: 1.371, power: -0.2559, loss: -2.2158, lr:0.0000454, took: 22.8290s
Batch 1899/3125, reward: 0.130, revenue_rate: 0.130, distance: 1.391, power: -0.2556, loss: 0.2526, lr:0.0000388, took: 23.2205s
Batch 1999/3125, reward: 0.130, revenue_rate: 0.130, distance: 1.379, power: -0.2557, loss: 0.2723, lr:0.0000322, took: 22.9736s
Batch 2099/3125, reward: 0.130, revenue_rate: 0.130, distance: 1.376, power: -0.2553, loss: -1.2406, lr:0.0000259, took: 23.4866s
Batch 2199/3125, reward: 0.131, revenue_rate: 0.131, distance: 1.385, power: -0.2569, loss: -0.2535, lr:0.0000200, took: 23.2921s
Batch 2299/3125, reward: 0.132, revenue_rate: 0.132, distance: 1.410, power: -0.2554, loss: -0.8257, lr:0.0000146, took: 23.7810s
Batch 2399/3125, reward: 0.132, revenue_rate: 0.132, distance: 1.400, power: -0.2561, loss: 0.1440, lr:0.0000100, took: 23.7031s
Batch 2499/3125, reward: 0.131, revenue_rate: 0.131, distance: 1.409, power: -0.2551, loss: -0.1565, lr:0.0000062, took: 23.6452s
Batch 2599/3125, reward: 0.132, revenue_rate: 0.132, distance: 1.396, power: -0.2557, loss: -0.2448, lr:0.0000034, took: 23.7959s
Batch 2699/3125, reward: 0.132, revenue_rate: 0.132, distance: 1.411, power: -0.2556, loss: 0.6928, lr:0.0000017, took: 23.6840s
Batch 2799/3125, reward: 0.131, revenue_rate: 0.131, distance: 1.402, power: -0.2558, loss: 0.4376, lr:0.0000010, took: 23.1417s
Batch 2899/3125, reward: 0.132, revenue_rate: 0.132, distance: 1.421, power: -0.2556, loss: 0.2644, lr:0.0000799, took: 23.6814s
Batch 2999/3125, reward: 0.133, revenue_rate: 0.133, distance: 1.423, power: -0.2543, loss: -1.4205, lr:0.0000795, took: 24.0144s
Batch 3099/3125, reward: 0.132, revenue_rate: 0.132, distance: 1.431, power: -0.2522, loss: 1.4095, lr:0.0000788, took: 24.1357s
Epoch 0 mean epoch loss/reward: 2.2282, 0.1173, -0.6746, took: 855.4501s (22.6605s / 100 batches)
Batch 99/3125, reward: 0.131, revenue_rate: 0.131, distance: 1.385, power: -0.2559, loss: 0.1092, lr:0.0000776, took: 22.8068s
Batch 199/3125, reward: 0.130, revenue_rate: 0.130, distance: 1.380, power: -0.2578, loss: 0.0399, lr:0.0000764, took: 23.2195s
Batch 299/3125, reward: 0.132, revenue_rate: 0.132, distance: 1.410, power: -0.2548, loss: -0.8686, lr:0.0000749, took: 24.0787s
Batch 399/3125, reward: 0.129, revenue_rate: 0.129, distance: 1.445, power: -0.2229, loss: 2.0500, lr:0.0000731, took: 24.0828s
Batch 499/3125, reward: 0.131, revenue_rate: 0.131, distance: 1.510, power: -0.2146, loss: 0.3210, lr:0.0000712, took: 25.1719s
Batch 599/3125, reward: 0.132, revenue_rate: 0.132, distance: 1.626, power: -0.1772, loss: 0.4049, lr:0.0000690, took: 25.6011s
Batch 699/3125, reward: 0.134, revenue_rate: 0.134, distance: 1.651, power: -0.1794, loss: 0.4162, lr:0.0000666, took: 26.2016s
Batch 799/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.727, power: -0.1092, loss: -0.8622, lr:0.0000640, took: 27.3417s
Batch 899/3125, reward: 0.132, revenue_rate: 0.132, distance: 1.695, power: -0.1426, loss: 1.7900, lr:0.0000613, took: 26.7465s
Batch 999/3125, reward: 0.131, revenue_rate: 0.131, distance: 1.734, power: -0.1215, loss: -1.1610, lr:0.0000584, took: 27.3302s
Batch 1099/3125, reward: 0.133, revenue_rate: 0.133, distance: 1.667, power: -0.1538, loss: 0.5097, lr:0.0000554, took: 26.8125s
Batch 1199/3125, reward: 0.138, revenue_rate: 0.138, distance: 1.595, power: -0.2151, loss: 0.6491, lr:0.0000522, took: 26.6821s
Batch 1299/3125, reward: 0.139, revenue_rate: 0.139, distance: 1.536, power: -0.2495, loss: 0.4519, lr:0.0000490, took: 26.8258s
Batch 1399/3125, reward: 0.137, revenue_rate: 0.137, distance: 1.481, power: -0.2553, loss: 0.3853, lr:0.0000458, took: 25.5239s
Batch 1499/3125, reward: 0.138, revenue_rate: 0.138, distance: 1.486, power: -0.2542, loss: 0.2201, lr:0.0000425, took: 25.5887s
Batch 1599/3125, reward: 0.137, revenue_rate: 0.137, distance: 1.497, power: -0.2534, loss: 0.8365, lr:0.0000392, took: 25.5675s
Batch 1699/3125, reward: 0.137, revenue_rate: 0.137, distance: 1.499, power: -0.2539, loss: -0.1444, lr:0.0000359, took: 25.0598s
Batch 1799/3125, reward: 0.138, revenue_rate: 0.138, distance: 1.507, power: -0.2533, loss: 0.4716, lr:0.0000326, took: 26.1712s
Batch 1899/3125, reward: 0.138, revenue_rate: 0.138, distance: 1.501, power: -0.2528, loss: 0.2442, lr:0.0000294, took: 25.5823s
Batch 1999/3125, reward: 0.138, revenue_rate: 0.138, distance: 1.507, power: -0.2523, loss: 0.8304, lr:0.0000263, took: 26.0367s
Batch 2099/3125, reward: 0.137, revenue_rate: 0.137, distance: 1.498, power: -0.2537, loss: -0.5574, lr:0.0000232, took: 25.5282s
Batch 2199/3125, reward: 0.138, revenue_rate: 0.138, distance: 1.505, power: -0.2529, loss: 0.5089, lr:0.0000203, took: 25.5897s
Batch 2299/3125, reward: 0.139, revenue_rate: 0.139, distance: 1.520, power: -0.2533, loss: 0.4535, lr:0.0000175, took: 26.1882s
Batch 2399/3125, reward: 0.139, revenue_rate: 0.139, distance: 1.527, power: -0.2528, loss: -0.1937, lr:0.0000149, took: 26.0647s
Batch 2499/3125, reward: 0.140, revenue_rate: 0.140, distance: 1.534, power: -0.2518, loss: -0.1714, lr:0.0000125, took: 26.1740s
Batch 2599/3125, reward: 0.139, revenue_rate: 0.139, distance: 1.533, power: -0.2516, loss: 0.1091, lr:0.0000103, took: 26.4819s
Batch 2699/3125, reward: 0.141, revenue_rate: 0.141, distance: 1.548, power: -0.2516, loss: -0.0881, lr:0.0000082, took: 26.4559s
Batch 2799/3125, reward: 0.140, revenue_rate: 0.140, distance: 1.544, power: -0.2508, loss: 0.5917, lr:0.0000064, took: 26.4662s
Batch 2899/3125, reward: 0.142, revenue_rate: 0.142, distance: 1.568, power: -0.2504, loss: 0.1392, lr:0.0000049, took: 27.0633s
Batch 2999/3125, reward: 0.141, revenue_rate: 0.141, distance: 1.553, power: -0.2490, loss: 0.2937, lr:0.0000036, took: 26.9892s
Batch 3099/3125, reward: 0.142, revenue_rate: 0.142, distance: 1.570, power: -0.2477, loss: 0.0669, lr:0.0000025, took: 27.1645s
Epoch 1 mean epoch loss/reward: 1.2448, 0.1268, -0.7771, took: 989.9940s (24.2754s / 100 batches)
Batch 99/3125, reward: 0.142, revenue_rate: 0.142, distance: 1.599, power: -0.2466, loss: 0.0738, lr:0.0000016, took: 25.7611s
Batch 199/3125, reward: 0.143, revenue_rate: 0.143, distance: 1.576, power: -0.2473, loss: 0.1424, lr:0.0000012, took: 27.4070s
Batch 299/3125, reward: 0.142, revenue_rate: 0.142, distance: 1.581, power: -0.2471, loss: -0.1663, lr:0.0000010, took: 27.1886s
Batch 399/3125, reward: 0.143, revenue_rate: 0.143, distance: 1.626, power: -0.2362, loss: -2.1163, lr:0.0000800, took: 27.7139s
Batch 499/3125, reward: 0.148, revenue_rate: 0.148, distance: 1.857, power: -0.1746, loss: 0.6648, lr:0.0000799, took: 30.0848s
Batch 599/3125, reward: 0.149, revenue_rate: 0.149, distance: 1.738, power: -0.2269, loss: -0.9551, lr:0.0000797, took: 29.5376s
Batch 699/3125, reward: 0.148, revenue_rate: 0.148, distance: 1.717, power: -0.2291, loss: 1.3537, lr:0.0000795, took: 29.5524s
Batch 799/3125, reward: 0.153, revenue_rate: 0.153, distance: 1.837, power: -0.2127, loss: -0.9090, lr:0.0000792, took: 30.9290s
Batch 899/3125, reward: 0.153, revenue_rate: 0.153, distance: 1.796, power: -0.2240, loss: -1.4438, lr:0.0000788, took: 30.6252s
Batch 999/3125, reward: 0.154, revenue_rate: 0.154, distance: 1.860, power: -0.2066, loss: 0.3070, lr:0.0000784, took: 31.2115s
Batch 1099/3125, reward: 0.159, revenue_rate: 0.159, distance: 2.094, power: -0.1511, loss: 0.4294, lr:0.0000778, took: 34.2173s
Batch 1199/3125, reward: 0.152, revenue_rate: 0.152, distance: 2.088, power: -0.0985, loss: -2.2766, lr:0.0000773, took: 34.2616s
Batch 1299/3125, reward: 0.171, revenue_rate: 0.171, distance: 2.382, power: -0.0990, loss: -0.7149, lr:0.0000766, took: 38.4343s
Batch 1399/3125, reward: 0.184, revenue_rate: 0.184, distance: 2.525, power: -0.1173, loss: 2.3679, lr:0.0000759, took: 41.2835s
Batch 1499/3125, reward: 0.195, revenue_rate: 0.195, distance: 2.683, power: -0.1178, loss: 1.1078, lr:0.0000752, took: 43.7964s
Batch 1599/3125, reward: 0.193, revenue_rate: 0.193, distance: 2.670, power: -0.1186, loss: -2.1181, lr:0.0000744, took: 42.7362s
Batch 1699/3125, reward: 0.202, revenue_rate: 0.202, distance: 2.800, power: -0.1139, loss: 4.9513, lr:0.0000735, took: 44.7166s
Batch 1799/3125, reward: 0.213, revenue_rate: 0.213, distance: 2.966, power: -0.1078, loss: 2.4397, lr:0.0000725, took: 46.3624s
Batch 1899/3125, reward: 0.241, revenue_rate: 0.241, distance: 3.370, power: -0.1092, loss: 2.3075, lr:0.0000715, took: 50.3587s
Batch 1999/3125, reward: 0.271, revenue_rate: 0.271, distance: 3.719, power: -0.1170, loss: -0.9933, lr:0.0000705, took: 54.5842s
Batch 2099/3125, reward: 0.281, revenue_rate: 0.281, distance: 3.982, power: -0.0842, loss: -4.2657, lr:0.0000694, took: 55.6259s
Batch 2199/3125, reward: 0.314, revenue_rate: 0.314, distance: 4.400, power: -0.0892, loss: 4.5437, lr:0.0000682, took: 61.1504s
Batch 2299/3125, reward: 0.352, revenue_rate: 0.352, distance: 4.928, power: -0.0933, loss: -0.3009, lr:0.0000670, took: 64.3860s
Batch 2399/3125, reward: 0.347, revenue_rate: 0.347, distance: 4.874, power: -0.0802, loss: 1.3713, lr:0.0000658, took: 63.7318s
Batch 2499/3125, reward: 0.335, revenue_rate: 0.335, distance: 4.711, power: -0.0797, loss: 2.4539, lr:0.0000645, took: 63.4618s
Batch 2599/3125, reward: 0.374, revenue_rate: 0.374, distance: 5.260, power: -0.0780, loss: 5.0621, lr:0.0000631, took: 69.0712s
Batch 2699/3125, reward: 0.383, revenue_rate: 0.383, distance: 5.409, power: -0.0703, loss: 1.7680, lr:0.0000618, took: 70.7229s
Batch 2799/3125, reward: 0.411, revenue_rate: 0.411, distance: 5.756, power: -0.0818, loss: -0.0797, lr:0.0000604, took: 77.3598s
Batch 2899/3125, reward: 0.402, revenue_rate: 0.402, distance: 5.616, power: -0.0876, loss: 1.1085, lr:0.0000589, took: 81.4454s
Batch 2999/3125, reward: 0.417, revenue_rate: 0.417, distance: 5.884, power: -0.0771, loss: -1.5992, lr:0.0000574, took: 88.7480s
Batch 3099/3125, reward: 0.423, revenue_rate: 0.423, distance: 6.035, power: -0.0532, loss: -0.0361, lr:0.0000559, took: 83.6561s
Epoch 2 mean epoch loss/reward: 0.9975, 0.1656, -0.8166, took: 1720.2216s (32.3139s / 100 batches)
