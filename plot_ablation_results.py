#!/usr/bin/env python
"""
消融实验结果绘图脚本
绘制平滑化的折线对照图，比较不同注意力机制的性能
"""

import os
import re
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from scipy.ndimage import gaussian_filter1d
from collections import defaultdict
import glob

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class AblationResultsPlotter:
    def __init__(self, results_dir="single_smp/single_smp100"):
        self.results_dir = results_dir
        self.experiment_data = {}
        self.colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
                      '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
        
    def parse_log_file(self, log_path):
        """解析单个log文件，提取训练数据"""
        data = {
            'config': {},
            'batches': [],
            'rewards': [],
            'revenue_rates': [],
            'distances': [],
            'powers': [],
            'losses': [],
            'epochs': []
        }
        
        try:
            with open(log_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # 解析配置信息
            for line in lines[:20]:  # 配置信息通常在前20行
                if ':' in line and not line.startswith('Batch'):
                    key, value = line.strip().split(':', 1)
                    data['config'][key.strip()] = value.strip()
            
            # 解析训练数据
            current_epoch = 0
            for line in lines:
                # 匹配batch数据行
                batch_match = re.match(
                    r'Batch (\d+)/\d+, reward: ([\d.-]+), revenue_rate: ([\d.-]+), '
                    r'distance: ([\d.-]+), power: ([\d.-]+), loss: ([\d.-]+)',
                    line.strip()
                )
                
                if batch_match:
                    batch_num = int(batch_match.group(1))
                    reward = float(batch_match.group(2))
                    revenue_rate = float(batch_match.group(3))
                    distance = float(batch_match.group(4))
                    power = float(batch_match.group(5))
                    loss = float(batch_match.group(6))
                    
                    data['batches'].append(batch_num + current_epoch * 3125)
                    data['rewards'].append(reward)
                    data['revenue_rates'].append(revenue_rate)
                    data['distances'].append(distance)
                    data['powers'].append(power)
                    data['losses'].append(loss)
                    data['epochs'].append(current_epoch)
                
                # 检测epoch结束
                elif 'Epoch' in line and 'mean epoch loss/reward' in line:
                    current_epoch += 1
                    
        except Exception as e:
            print(f"解析文件 {log_path} 时出错: {e}")
            
        return data
    
    def load_all_experiments(self):
        """加载所有实验数据"""
        if not os.path.exists(self.results_dir):
            print(f"结果目录不存在: {self.results_dir}")
            return
            
        # 获取所有实验目录
        exp_dirs = [d for d in os.listdir(self.results_dir) 
                   if os.path.isdir(os.path.join(self.results_dir, d))]
        
        for exp_dir in exp_dirs:
            log_path = os.path.join(self.results_dir, exp_dir, 'log.txt')
            if os.path.exists(log_path):
                print(f"加载实验: {exp_dir}")
                data = self.parse_log_file(log_path)
                
                # 根据attention_type确定实验名称
                attention_type = data['config'].get('attention_type', 'unknown')
                n_head = data['config'].get('n_head', '1')
                
                if attention_type == 'multi_head':
                    exp_name = f"多头注意力 (n_head={n_head})"
                elif attention_type == 'single_head':
                    exp_name = f"单头注意力 (n_head={n_head})"
                elif attention_type == 'no_layer_norm':
                    exp_name = f"无层归一化 (n_head={n_head})"
                elif attention_type == 'no_scale':
                    exp_name = f"无缩放因子 (n_head={n_head})"
                elif attention_type == 'configurable':
                    exp_name = f"可配置注意力 (n_head={n_head})"
                else:
                    exp_name = f"{attention_type} (n_head={n_head})"
                
                self.experiment_data[exp_name] = data
                
        print(f"成功加载 {len(self.experiment_data)} 个实验")
    
    def smooth_data(self, data, sigma=2.0):
        """使用高斯滤波平滑数据"""
        if len(data) < 3:
            return data
        return gaussian_filter1d(np.array(data), sigma=sigma)
    
    def plot_comparison(self, metric='rewards', smooth=True, sigma=2.0, 
                       save_path=None, figsize=(12, 8)):
        """绘制对比图"""
        if not self.experiment_data:
            print("没有加载到实验数据")
            return
            
        plt.figure(figsize=figsize)
        
        # 指标名称映射
        metric_names = {
            'rewards': '奖励 (Reward)',
            'revenue_rates': '收益率 (Revenue Rate)', 
            'distances': '距离 (Distance)',
            'powers': '功率 (Power)',
            'losses': '损失 (Loss)'
        }
        
        for i, (exp_name, data) in enumerate(self.experiment_data.items()):
            if metric not in data or not data[metric]:
                continue
                
            x_data = np.array(data['batches'])
            y_data = np.array(data[metric])
            
            if smooth and len(y_data) > 3:
                y_data = self.smooth_data(y_data, sigma=sigma)
            
            color = self.colors[i % len(self.colors)]
            plt.plot(x_data, y_data, label=exp_name, color=color, linewidth=2)
        
        plt.xlabel('训练批次 (Batch)', fontsize=12)
        plt.ylabel(metric_names.get(metric, metric), fontsize=12)
        plt.title(f'消融实验结果对比 - {metric_names.get(metric, metric)}', fontsize=14, fontweight='bold')
        plt.legend(fontsize=10)
        plt.grid(True, alpha=0.3)
        
        # 添加epoch分割线
        if self.experiment_data:
            first_exp = list(self.experiment_data.values())[0]
            for epoch in range(1, 3):  # 假设有3个epoch
                plt.axvline(x=epoch*3125, color='gray', linestyle='--', alpha=0.5)
                plt.text(epoch*3125, plt.ylim()[1]*0.95, f'Epoch {epoch}', 
                        rotation=90, fontsize=9, alpha=0.7)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"图表已保存到: {save_path}")
        
        plt.show()
    
    def plot_multi_metrics(self, metrics=['rewards', 'losses'], smooth=True, 
                          sigma=2.0, save_path=None, figsize=(15, 10)):
        """绘制多指标对比图"""
        if not self.experiment_data:
            print("没有加载到实验数据")
            return
            
        n_metrics = len(metrics)
        fig, axes = plt.subplots(n_metrics, 1, figsize=figsize, sharex=True)
        
        if n_metrics == 1:
            axes = [axes]
        
        metric_names = {
            'rewards': '奖励 (Reward)',
            'revenue_rates': '收益率 (Revenue Rate)', 
            'distances': '距离 (Distance)',
            'powers': '功率 (Power)',
            'losses': '损失 (Loss)'
        }
        
        for metric_idx, metric in enumerate(metrics):
            ax = axes[metric_idx]
            
            for i, (exp_name, data) in enumerate(self.experiment_data.items()):
                if metric not in data or not data[metric]:
                    continue
                    
                x_data = np.array(data['batches'])
                y_data = np.array(data[metric])
                
                if smooth and len(y_data) > 3:
                    y_data = self.smooth_data(y_data, sigma=sigma)
                
                color = self.colors[i % len(self.colors)]
                ax.plot(x_data, y_data, label=exp_name, color=color, linewidth=2)
            
            ax.set_ylabel(metric_names.get(metric, metric), fontsize=12)
            ax.grid(True, alpha=0.3)
            
            if metric_idx == 0:
                ax.legend(fontsize=10)
                ax.set_title('消融实验结果对比 - 多指标', fontsize=14, fontweight='bold')
            
            # 添加epoch分割线
            if self.experiment_data:
                for epoch in range(1, 3):
                    ax.axvline(x=epoch*3125, color='gray', linestyle='--', alpha=0.5)
                    if metric_idx == 0:
                        ax.text(epoch*3125, ax.get_ylim()[1]*0.95, f'Epoch {epoch}', 
                               rotation=90, fontsize=9, alpha=0.7)
        
        axes[-1].set_xlabel('训练批次 (Batch)', fontsize=12)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"图表已保存到: {save_path}")
        
        plt.show()
    
    def generate_summary_report(self):
        """生成实验总结报告"""
        if not self.experiment_data:
            print("没有加载到实验数据")
            return
            
        print("\n" + "="*60)
        print("消融实验结果总结报告")
        print("="*60)
        
        for exp_name, data in self.experiment_data.items():
            print(f"\n实验: {exp_name}")
            print("-" * 40)
            
            if data['rewards']:
                final_reward = data['rewards'][-1]
                max_reward = max(data['rewards'])
                avg_reward = np.mean(data['rewards'])
                
                print(f"最终奖励: {final_reward:.4f}")
                print(f"最大奖励: {max_reward:.4f}")
                print(f"平均奖励: {avg_reward:.4f}")
                
            if data['losses']:
                final_loss = data['losses'][-1]
                min_loss = min(data['losses'])
                avg_loss = np.mean(data['losses'])
                
                print(f"最终损失: {final_loss:.4f}")
                print(f"最小损失: {min_loss:.4f}")
                print(f"平均损失: {avg_loss:.4f}")


def main():
    """主函数"""
    plotter = AblationResultsPlotter()
    
    # 加载所有实验数据
    plotter.load_all_experiments()
    
    if not plotter.experiment_data:
        print("未找到实验数据，请检查路径是否正确")
        return
    
    # 生成总结报告
    plotter.generate_summary_report()
    
    # 绘制奖励对比图
    print("\n绘制奖励对比图...")
    plotter.plot_comparison(
        metric='rewards', 
        smooth=True, 
        sigma=3.0,
        save_path='ablation_rewards_comparison.png'
    )
    
    # 绘制损失对比图
    print("\n绘制损失对比图...")
    plotter.plot_comparison(
        metric='losses', 
        smooth=True, 
        sigma=3.0,
        save_path='ablation_losses_comparison.png'
    )
    
    # 绘制多指标对比图
    print("\n绘制多指标对比图...")
    plotter.plot_multi_metrics(
        metrics=['rewards', 'losses', 'revenue_rates'],
        smooth=True,
        sigma=3.0,
        save_path='ablation_multi_metrics_comparison.png'
    )


if __name__ == "__main__":
    main()
