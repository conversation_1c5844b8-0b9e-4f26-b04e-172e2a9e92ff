single_smp: 100
model: gpn
rnn: indrnn
hidden_size: 256
batch_size: 32
seed: 12345
train-size: 100000
valid-size: 10000
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 8e-05
critic_lr: 8e-05
attention_type: multi_head
n_head: 8
2025_06_23_11_17_28
Batch 99/3125, reward: 0.067, revenue_rate: 0.067, distance: 1.160, power: 0.0146, loss: 72.8460, lr:0.0000778, took: 21.0104s
Batch 199/3125, reward: 0.070, revenue_rate: 0.070, distance: 1.224, power: 0.0193, loss: -3.7101, lr:0.0000714, took: 21.6037s
Batch 299/3125, reward: 0.077, revenue_rate: 0.077, distance: 1.313, power: 0.0158, loss: 1.3635, lr:0.0000616, took: 22.6504s
<PERSON>ch 399/3125, reward: 0.083, revenue_rate: 0.083, distance: 1.400, power: 0.0182, loss: 2.5471, lr:0.0000495, took: 23.0836s
Batch 499/3125, reward: 0.088, revenue_rate: 0.088, distance: 1.483, power: 0.0175, loss: -0.8989, lr:0.0000363, took: 24.3054s
Batch 599/3125, reward: 0.087, revenue_rate: 0.087, distance: 1.461, power: 0.0160, loss: 1.4070, lr:0.0000236, took: 24.0636s
Batch 699/3125, reward: 0.083, revenue_rate: 0.083, distance: 1.403, power: 0.0160, loss: 3.0556, lr:0.0000128, took: 23.0936s
Batch 799/3125, reward: 0.082, revenue_rate: 0.082, distance: 1.376, power: 0.0149, loss: 1.1164, lr:0.0000051, took: 23.2811s
Batch 899/3125, reward: 0.082, revenue_rate: 0.082, distance: 1.397, power: 0.0157, loss: 2.5222, lr:0.0000013, took: 23.3552s
Batch 999/3125, reward: 0.079, revenue_rate: 0.079, distance: 1.348, power: 0.0162, loss: -2.2761, lr:0.0000798, took: 22.6951s
Batch 1099/3125, reward: 0.112, revenue_rate: 0.112, distance: 1.337, power: -0.1635, loss: -1.7635, lr:0.0000785, took: 23.0202s
Batch 1199/3125, reward: 0.125, revenue_rate: 0.125, distance: 1.333, power: -0.2427, loss: 0.7875, lr:0.0000762, took: 22.5715s
Batch 1299/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.336, power: -0.2593, loss: 0.4755, lr:0.0000729, took: 22.9332s
Batch 1399/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.340, power: -0.2587, loss: -1.2109, lr:0.0000687, took: 22.4691s
Batch 1499/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.324, power: -0.2600, loss: 1.6907, lr:0.0000637, took: 22.9006s
Batch 1599/3125, reward: 0.129, revenue_rate: 0.129, distance: 1.358, power: -0.2573, loss: 0.6436, lr:0.0000580, took: 24.0232s
Batch 1699/3125, reward: 0.130, revenue_rate: 0.130, distance: 1.386, power: -0.2567, loss: 0.9469, lr:0.0000519, took: 23.5983s
Batch 1799/3125, reward: 0.130, revenue_rate: 0.130, distance: 1.386, power: -0.2561, loss: 0.0777, lr:0.0000454, took: 23.8937s
Batch 1899/3125, reward: 0.131, revenue_rate: 0.131, distance: 1.387, power: -0.2558, loss: 0.1962, lr:0.0000388, took: 23.6580s
Batch 1999/3125, reward: 0.129, revenue_rate: 0.129, distance: 1.360, power: -0.2574, loss: 0.6756, lr:0.0000322, took: 23.5875s
Batch 2099/3125, reward: 0.129, revenue_rate: 0.129, distance: 1.377, power: -0.2561, loss: 1.0118, lr:0.0000259, took: 23.8389s
Batch 2199/3125, reward: 0.131, revenue_rate: 0.131, distance: 1.396, power: -0.2561, loss: -1.6953, lr:0.0000200, took: 23.9657s
Batch 2299/3125, reward: 0.132, revenue_rate: 0.132, distance: 1.402, power: -0.2562, loss: 0.0038, lr:0.0000146, took: 24.1469s
Batch 2399/3125, reward: 0.131, revenue_rate: 0.131, distance: 1.396, power: -0.2562, loss: -0.2832, lr:0.0000100, took: 24.1091s
Batch 2499/3125, reward: 0.131, revenue_rate: 0.131, distance: 1.385, power: -0.2560, loss: -0.1348, lr:0.0000062, took: 24.0958s
Batch 2599/3125, reward: 0.131, revenue_rate: 0.131, distance: 1.401, power: -0.2555, loss: -0.3741, lr:0.0000034, took: 24.0410s
Batch 2699/3125, reward: 0.130, revenue_rate: 0.130, distance: 1.402, power: -0.2560, loss: 0.8412, lr:0.0000017, took: 23.5377s
Batch 2799/3125, reward: 0.130, revenue_rate: 0.130, distance: 1.387, power: -0.2559, loss: 0.3203, lr:0.0000010, took: 23.9169s
Batch 2899/3125, reward: 0.130, revenue_rate: 0.130, distance: 1.388, power: -0.2567, loss: 1.8869, lr:0.0000799, took: 23.2615s
Batch 2999/3125, reward: 0.109, revenue_rate: 0.109, distance: 1.411, power: -0.1295, loss: 1.9822, lr:0.0000795, took: 24.1231s
Batch 3099/3125, reward: 0.078, revenue_rate: 0.078, distance: 1.335, power: 0.0141, loss: -0.2242, lr:0.0000788, took: 22.9992s
Epoch 0 mean epoch loss/reward: 2.6939, 0.1103, -0.8185, took: 942.6029s (23.3495s / 100 batches)
Batch 99/3125, reward: 0.080, revenue_rate: 0.080, distance: 1.362, power: 0.0102, loss: -0.8001, lr:0.0000776, took: 22.4764s
Batch 199/3125, reward: 0.124, revenue_rate: 0.124, distance: 1.518, power: -0.1669, loss: -1.0713, lr:0.0000764, took: 25.3674s
Batch 299/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.354, power: -0.2557, loss: 0.0558, lr:0.0000749, took: 23.2920s
Batch 399/3125, reward: 0.125, revenue_rate: 0.125, distance: 1.365, power: -0.2347, loss: 3.2722, lr:0.0000731, took: 23.2188s
Batch 499/3125, reward: 0.131, revenue_rate: 0.131, distance: 1.395, power: -0.2554, loss: 0.3710, lr:0.0000712, took: 24.1317s
Batch 599/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.367, power: -0.2576, loss: -0.5826, lr:0.0000690, took: 23.0567s
Batch 699/3125, reward: 0.129, revenue_rate: 0.129, distance: 1.368, power: -0.2590, loss: -0.4626, lr:0.0000666, took: 22.9117s
Batch 799/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.347, power: -0.2581, loss: -0.2542, lr:0.0000640, took: 23.1535s
Batch 899/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.345, power: -0.2579, loss: 0.3840, lr:0.0000613, took: 23.2381s
Batch 999/3125, reward: 0.121, revenue_rate: 0.121, distance: 1.388, power: -0.2068, loss: -1.9509, lr:0.0000584, took: 22.8596s
Batch 1099/3125, reward: 0.126, revenue_rate: 0.126, distance: 1.384, power: -0.2306, loss: -0.0827, lr:0.0000554, took: 23.0132s
Batch 1199/3125, reward: 0.123, revenue_rate: 0.123, distance: 1.418, power: -0.1957, loss: 2.3101, lr:0.0000522, took: 23.7487s
Batch 1299/3125, reward: 0.122, revenue_rate: 0.122, distance: 1.441, power: -0.1840, loss: 0.0701, lr:0.0000490, took: 23.4906s
Batch 1399/3125, reward: 0.125, revenue_rate: 0.125, distance: 1.410, power: -0.2172, loss: -0.0150, lr:0.0000458, took: 23.0021s
Batch 1499/3125, reward: 0.126, revenue_rate: 0.126, distance: 1.406, power: -0.2193, loss: 0.4660, lr:0.0000425, took: 23.3372s
Batch 1599/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.398, power: -0.2333, loss: 1.0200, lr:0.0000392, took: 23.4152s
Batch 1699/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.380, power: -0.2459, loss: -0.7991, lr:0.0000359, took: 22.9222s
Batch 1799/3125, reward: 0.124, revenue_rate: 0.124, distance: 1.431, power: -0.2041, loss: 0.4410, lr:0.0000326, took: 23.4423s
Batch 1899/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.368, power: -0.2512, loss: -1.1334, lr:0.0000294, took: 23.3515s
Batch 1999/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.350, power: -0.2570, loss: 2.0038, lr:0.0000263, took: 22.9541s
Batch 2099/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.348, power: -0.2582, loss: -0.5743, lr:0.0000232, took: 23.5379s
Batch 2199/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.348, power: -0.2583, loss: 0.1241, lr:0.0000203, took: 23.0675s
Batch 2299/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.338, power: -0.2588, loss: -0.2963, lr:0.0000175, took: 23.3671s
Batch 2399/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.341, power: -0.2584, loss: -0.0789, lr:0.0000149, took: 27.4983s
Batch 2499/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.350, power: -0.2577, loss: 0.0982, lr:0.0000125, took: 24.3546s
Batch 2599/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.336, power: -0.2582, loss: 0.0979, lr:0.0000103, took: 23.4436s
Batch 2699/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.348, power: -0.2584, loss: 0.6454, lr:0.0000082, took: 23.8085s
Batch 2799/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.331, power: -0.2582, loss: 0.9978, lr:0.0000064, took: 23.5785s
Batch 2899/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.336, power: -0.2594, loss: -0.2749, lr:0.0000049, took: 23.1412s
Batch 2999/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.326, power: -0.2589, loss: -0.2247, lr:0.0000036, took: 23.7621s
Batch 3099/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.334, power: -0.2578, loss: -0.0282, lr:0.0000025, took: 23.5174s
Epoch 1 mean epoch loss/reward: 1.4081, 0.1178, -0.7211, took: 903.0380s (23.4402s / 100 batches)
Batch 99/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.346, power: -0.2582, loss: -0.1801, lr:0.0000016, took: 22.0945s
Batch 199/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.336, power: -0.2586, loss: 0.3417, lr:0.0000012, took: 23.2272s
Batch 299/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.325, power: -0.2594, loss: -0.0635, lr:0.0000010, took: 23.0794s
Batch 399/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.340, power: -0.2587, loss: 0.4728, lr:0.0000800, took: 23.8502s
Batch 499/3125, reward: 0.132, revenue_rate: 0.132, distance: 1.411, power: -0.2566, loss: 0.3735, lr:0.0000799, took: 24.6573s
Batch 599/3125, reward: 0.136, revenue_rate: 0.136, distance: 1.459, power: -0.2547, loss: -0.7946, lr:0.0000797, took: 25.8063s
Batch 699/3125, reward: 0.135, revenue_rate: 0.135, distance: 1.461, power: -0.2541, loss: 0.5647, lr:0.0000795, took: 25.4021s
Batch 799/3125, reward: 0.136, revenue_rate: 0.136, distance: 1.481, power: -0.2542, loss: -0.0607, lr:0.0000792, took: 26.2883s
Batch 899/3125, reward: 0.135, revenue_rate: 0.135, distance: 1.473, power: -0.2534, loss: 0.5944, lr:0.0000788, took: 25.6061s
Batch 999/3125, reward: 0.130, revenue_rate: 0.130, distance: 1.386, power: -0.2547, loss: -0.1355, lr:0.0000784, took: 24.0734s
Batch 1099/3125, reward: 0.129, revenue_rate: 0.129, distance: 1.356, power: -0.2575, loss: 0.8826, lr:0.0000778, took: 23.7701s
Batch 1199/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.339, power: -0.2522, loss: -0.1883, lr:0.0000773, took: 23.7992s
Batch 1299/3125, reward: 0.129, revenue_rate: 0.129, distance: 1.365, power: -0.2568, loss: 0.6683, lr:0.0000766, took: 23.5158s
Batch 1399/3125, reward: 0.129, revenue_rate: 0.129, distance: 1.357, power: -0.2581, loss: 0.0217, lr:0.0000759, took: 23.3078s
Batch 1499/3125, reward: 0.108, revenue_rate: 0.108, distance: 1.399, power: -0.1290, loss: 1.3214, lr:0.0000752, took: 23.7461s
Batch 1599/3125, reward: 0.081, revenue_rate: 0.081, distance: 1.389, power: 0.0166, loss: -0.4504, lr:0.0000744, took: 24.2632s
Batch 1699/3125, reward: 0.087, revenue_rate: 0.087, distance: 1.474, power: 0.0116, loss: -0.3959, lr:0.0000735, took: 24.2359s
Batch 1799/3125, reward: 0.099, revenue_rate: 0.099, distance: 1.511, power: -0.0346, loss: 0.3922, lr:0.0000725, took: 25.6123s
Batch 1899/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.409, power: -0.2272, loss: -0.3068, lr:0.0000715, took: 24.4221s
Batch 1999/3125, reward: 0.129, revenue_rate: 0.129, distance: 1.369, power: -0.2573, loss: 1.8117, lr:0.0000705, took: 24.3292s
Batch 2099/3125, reward: 0.129, revenue_rate: 0.129, distance: 1.372, power: -0.2575, loss: 0.3263, lr:0.0000694, took: 24.0409s
Batch 2199/3125, reward: 0.131, revenue_rate: 0.131, distance: 1.400, power: -0.2563, loss: 1.7514, lr:0.0000682, took: 24.5290s
Batch 2299/3125, reward: 0.135, revenue_rate: 0.135, distance: 1.507, power: -0.2386, loss: 0.5686, lr:0.0000670, took: 26.5361s
Batch 2399/3125, reward: 0.138, revenue_rate: 0.138, distance: 1.506, power: -0.2528, loss: 0.2246, lr:0.0000658, took: 26.9036s
Batch 2499/3125, reward: 0.139, revenue_rate: 0.139, distance: 1.538, power: -0.2521, loss: 0.0317, lr:0.0000645, took: 27.5984s
Batch 2599/3125, reward: 0.140, revenue_rate: 0.140, distance: 1.531, power: -0.2523, loss: 0.1736, lr:0.0000631, took: 27.0807s
Batch 2699/3125, reward: 0.140, revenue_rate: 0.140, distance: 1.536, power: -0.2523, loss: 0.2443, lr:0.0000618, took: 27.3077s
Batch 2799/3125, reward: 0.139, revenue_rate: 0.139, distance: 1.521, power: -0.2527, loss: 0.0717, lr:0.0000604, took: 27.2296s
Batch 2899/3125, reward: 0.141, revenue_rate: 0.141, distance: 1.546, power: -0.2518, loss: -0.3092, lr:0.0000589, took: 27.9723s
Batch 2999/3125, reward: 0.141, revenue_rate: 0.141, distance: 1.533, power: -0.2517, loss: -1.0834, lr:0.0000574, took: 28.1090s
Batch 3099/3125, reward: 0.141, revenue_rate: 0.141, distance: 1.558, power: -0.2516, loss: -1.0293, lr:0.0000559, took: 28.4639s
Epoch 2 mean epoch loss/reward: 1.0042, 0.1213, -0.7758, took: 979.2367s (24.0231s / 100 batches)
