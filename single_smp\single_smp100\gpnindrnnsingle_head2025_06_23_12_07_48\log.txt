single_smp: 100
model: gpn
rnn: indrnn
hidden_size: 256
batch_size: 32
seed: 12345
train-size: 100000
valid-size: 10000
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 8e-05
critic_lr: 8e-05
attention_type: single_head
n_head: 1
2025_06_23_12_07_48
Batch 99/3125, reward: 0.094, revenue_rate: 0.094, distance: 1.615, power: 0.0221, loss: -0.4919, lr:0.0000778, took: 26.7816s
Batch 199/3125, reward: 0.099, revenue_rate: 0.099, distance: 1.677, power: 0.0166, loss: -1.2148, lr:0.0000714, took: 27.8196s
Batch 299/3125, reward: 0.119, revenue_rate: 0.119, distance: 1.755, power: -0.0546, loss: -0.0725, lr:0.0000616, took: 28.8718s
Batch 399/3125, reward: 0.132, revenue_rate: 0.132, distance: 1.590, power: -0.1921, loss: -0.2583, lr:0.0000495, took: 27.2546s
Batch 499/3125, reward: 0.132, revenue_rate: 0.132, distance: 1.396, power: -0.2566, loss: -0.0104, lr:0.0000363, took: 24.7152s
Batch 599/3125, reward: 0.111, revenue_rate: 0.111, distance: 1.389, power: -0.1390, loss: 0.1124, lr:0.0000236, took: 24.3422s
Batch 699/3125, reward: 0.126, revenue_rate: 0.126, distance: 1.342, power: -0.2459, loss: 0.4434, lr:0.0000128, took: 23.4346s
Batch 799/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.353, power: -0.2573, loss: 0.0767, lr:0.0000051, took: 24.1130s
Batch 899/3125, reward: 0.129, revenue_rate: 0.129, distance: 1.365, power: -0.2568, loss: -0.1180, lr:0.0000013, took: 24.3502s
Batch 999/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.340, power: -0.2555, loss: -1.6043, lr:0.0000798, took: 24.5975s
Batch 1099/3125, reward: 0.116, revenue_rate: 0.116, distance: 1.303, power: -0.2069, loss: -0.1797, lr:0.0000785, took: 23.2351s
Batch 1199/3125, reward: 0.125, revenue_rate: 0.125, distance: 1.412, power: -0.2176, loss: -0.4177, lr:0.0000762, took: 26.0334s
Batch 1299/3125, reward: 0.141, revenue_rate: 0.141, distance: 1.548, power: -0.2521, loss: 0.3083, lr:0.0000729, took: 28.1169s
Batch 1399/3125, reward: 0.141, revenue_rate: 0.141, distance: 1.551, power: -0.2507, loss: 0.3372, lr:0.0000687, took: 28.7198s
Batch 1499/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.526, power: -0.1824, loss: -0.5412, lr:0.0000637, took: 27.4120s
Batch 1599/3125, reward: 0.111, revenue_rate: 0.111, distance: 1.498, power: -0.1120, loss: 0.4807, lr:0.0000580, took: 26.2170s
Batch 1699/3125, reward: 0.140, revenue_rate: 0.140, distance: 1.552, power: -0.2482, loss: 0.1361, lr:0.0000519, took: 29.5824s
Batch 1799/3125, reward: 0.129, revenue_rate: 0.129, distance: 1.338, power: -0.2583, loss: 0.4598, lr:0.0000454, took: 24.9205s
Batch 1899/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.354, power: -0.2572, loss: -0.1157, lr:0.0000388, took: 24.5336s
Batch 1999/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.337, power: -0.2587, loss: 0.4437, lr:0.0000322, took: 24.8891s
Batch 2099/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.328, power: -0.2586, loss: 0.4847, lr:0.0000259, took: 25.5640s
Batch 2199/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.339, power: -0.2596, loss: 0.2467, lr:0.0000200, took: 25.9777s
Batch 2299/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.336, power: -0.2594, loss: 0.4342, lr:0.0000146, took: 24.1306s
Batch 2399/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.330, power: -0.2580, loss: 0.2121, lr:0.0000100, took: 23.8615s
Batch 2499/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.334, power: -0.2585, loss: 0.1946, lr:0.0000062, took: 23.6979s
Batch 2599/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.348, power: -0.2580, loss: 0.0332, lr:0.0000034, took: 23.4630s
Batch 2699/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.346, power: -0.2581, loss: -0.0703, lr:0.0000017, took: 23.5795s
Batch 2799/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.342, power: -0.2581, loss: -0.0157, lr:0.0000010, took: 23.4346s
Batch 2899/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.332, power: -0.2585, loss: 0.8809, lr:0.0000799, took: 23.6471s
Batch 2999/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.352, power: -0.2582, loss: 0.0447, lr:0.0000795, took: 24.2315s
Batch 3099/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.351, power: -0.2582, loss: -0.0446, lr:0.0000788, took: 23.7560s
Epoch 0 mean epoch loss/reward: 0.0250, 0.1253, -0.6817, took: 948.2021s (25.3317s / 100 batches)
Batch 99/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.340, power: -0.2580, loss: 0.7672, lr:0.0000776, took: 23.3470s
Batch 199/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.342, power: -0.2582, loss: -0.5682, lr:0.0000764, took: 23.7704s
Batch 299/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.341, power: -0.2577, loss: 0.4714, lr:0.0000749, took: 23.8749s
Batch 399/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.338, power: -0.2582, loss: -0.5156, lr:0.0000731, took: 23.7531s
Batch 499/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.338, power: -0.2582, loss: 0.4683, lr:0.0000712, took: 23.8298s
Batch 599/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.345, power: -0.2575, loss: 0.7495, lr:0.0000690, took: 23.8192s
Batch 699/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.330, power: -0.2585, loss: -0.0597, lr:0.0000666, took: 23.8565s
Batch 799/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.354, power: -0.2590, loss: 0.5166, lr:0.0000640, took: 23.6860s
Batch 899/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.337, power: -0.2585, loss: 0.2115, lr:0.0000613, took: 23.1838s
Batch 999/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.340, power: -0.2589, loss: 0.4348, lr:0.0000584, took: 23.9947s
Batch 1099/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.328, power: -0.2593, loss: 0.1358, lr:0.0000554, took: 23.5808s
Batch 1199/3125, reward: 0.126, revenue_rate: 0.126, distance: 1.326, power: -0.2590, loss: -0.0049, lr:0.0000522, took: 25.4956s
Batch 1299/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.333, power: -0.2584, loss: 0.0217, lr:0.0000490, took: 24.7586s
Batch 1399/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.340, power: -0.2585, loss: 0.4654, lr:0.0000458, took: 23.7599s
Batch 1499/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.344, power: -0.2593, loss: 0.1898, lr:0.0000425, took: 23.6200s
Batch 1599/3125, reward: 0.126, revenue_rate: 0.126, distance: 1.329, power: -0.2579, loss: -0.0931, lr:0.0000392, took: 23.8650s
Batch 1699/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.339, power: -0.2590, loss: -0.2080, lr:0.0000359, took: 23.6100s
Batch 1799/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.347, power: -0.2585, loss: -0.1332, lr:0.0000326, took: 23.6721s
Batch 1899/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.334, power: -0.2592, loss: 0.3392, lr:0.0000294, took: 23.4099s
Batch 1999/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.336, power: -0.2584, loss: 0.0175, lr:0.0000263, took: 23.9568s
Batch 2099/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.334, power: -0.2580, loss: -0.0262, lr:0.0000232, took: 23.5351s
Batch 2199/3125, reward: 0.129, revenue_rate: 0.129, distance: 1.369, power: -0.2584, loss: -0.0631, lr:0.0000203, took: 24.3508s
Batch 2299/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.333, power: -0.2593, loss: 0.0155, lr:0.0000175, took: 23.9394s
Batch 2399/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.347, power: -0.2581, loss: 0.2027, lr:0.0000149, took: 23.5182s
Batch 2499/3125, reward: 0.126, revenue_rate: 0.126, distance: 1.317, power: -0.2590, loss: 0.2147, lr:0.0000125, took: 23.3907s
Batch 2599/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.332, power: -0.2580, loss: 0.3911, lr:0.0000103, took: 23.2311s
Batch 2699/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.333, power: -0.2580, loss: 0.1737, lr:0.0000082, took: 24.0233s
Batch 2799/3125, reward: 0.126, revenue_rate: 0.126, distance: 1.324, power: -0.2579, loss: 0.0411, lr:0.0000064, took: 23.4064s
Batch 2899/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.341, power: -0.2590, loss: 0.0295, lr:0.0000049, took: 23.7467s
Batch 2999/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.352, power: -0.2581, loss: 0.1962, lr:0.0000036, took: 23.4985s
Batch 3099/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.326, power: -0.2592, loss: 0.0893, lr:0.0000025, took: 23.3422s
Epoch 1 mean epoch loss/reward: 0.0841, 0.1263, -0.6814, took: 898.0716s (24.5502s / 100 batches)
Batch 99/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.320, power: -0.2591, loss: 0.0901, lr:0.0000016, took: 22.9299s
Batch 199/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.339, power: -0.2590, loss: -0.0521, lr:0.0000012, took: 23.6011s
Batch 299/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.331, power: -0.2590, loss: 0.2203, lr:0.0000010, took: 23.4362s
Batch 399/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.352, power: -0.2578, loss: 0.5333, lr:0.0000800, took: 23.8706s
Batch 499/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.351, power: -0.2580, loss: 1.3873, lr:0.0000799, took: 23.6103s
Batch 599/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.346, power: -0.2578, loss: 0.7517, lr:0.0000797, took: 23.8198s
Batch 699/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.331, power: -0.2592, loss: 1.0645, lr:0.0000795, took: 23.9299s
Batch 799/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.362, power: -0.2573, loss: 0.9890, lr:0.0000792, took: 24.0861s
Batch 899/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.340, power: -0.2588, loss: 0.7705, lr:0.0000788, took: 24.2164s
Batch 999/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.335, power: -0.2598, loss: -0.8411, lr:0.0000784, took: 23.8031s
Batch 1099/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.336, power: -0.2586, loss: -0.3643, lr:0.0000778, took: 23.6344s
Batch 1199/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.343, power: -0.2585, loss: 0.2581, lr:0.0000773, took: 23.7359s
Batch 1299/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.344, power: -0.2591, loss: 0.7988, lr:0.0000766, took: 23.6435s
Batch 1399/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.340, power: -0.2585, loss: 0.1314, lr:0.0000759, took: 23.7740s
Batch 1499/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.333, power: -0.2586, loss: -0.0946, lr:0.0000752, took: 23.9885s
Batch 1599/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.345, power: -0.2588, loss: -0.3737, lr:0.0000744, took: 23.9152s
Batch 1699/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.349, power: -0.2576, loss: -0.1324, lr:0.0000735, took: 23.6176s
Batch 1799/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.342, power: -0.2583, loss: -0.1944, lr:0.0000725, took: 23.8404s
Batch 1899/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.322, power: -0.2593, loss: -0.0661, lr:0.0000715, took: 23.6404s
Batch 1999/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.337, power: -0.2579, loss: -0.0155, lr:0.0000705, took: 23.7716s
Batch 2099/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.338, power: -0.2587, loss: 0.1673, lr:0.0000694, took: 23.6459s
Batch 2199/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.354, power: -0.2580, loss: 0.2325, lr:0.0000682, took: 23.9741s
Batch 2299/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.341, power: -0.2584, loss: 0.1638, lr:0.0000670, took: 23.5322s
Batch 2399/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.344, power: -0.2581, loss: 0.5278, lr:0.0000658, took: 23.4850s
Batch 2499/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.343, power: -0.2587, loss: 0.5596, lr:0.0000645, took: 23.5295s
Batch 2599/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.332, power: -0.2579, loss: 0.2518, lr:0.0000631, took: 23.7253s
Batch 2699/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.353, power: -0.2586, loss: 0.2602, lr:0.0000618, took: 23.7636s
Batch 2799/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.335, power: -0.2574, loss: -0.0450, lr:0.0000604, took: 23.4364s
Batch 2899/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.326, power: -0.2573, loss: 0.4952, lr:0.0000589, took: 23.4477s
Batch 2999/3125, reward: 0.127, revenue_rate: 0.127, distance: 1.332, power: -0.2584, loss: -0.1294, lr:0.0000574, took: 23.6403s
Batch 3099/3125, reward: 0.128, revenue_rate: 0.128, distance: 1.350, power: -0.2589, loss: 0.1419, lr:0.0000559, took: 23.7473s
Epoch 2 mean epoch loss/reward: 0.1362, 0.1266, -0.0827, took: 777.8888s (24.2678s / 100 batches)
