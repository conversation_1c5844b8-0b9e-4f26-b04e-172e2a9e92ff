single_smp: 100
model: gpn
rnn: indrnn
hidden_size: 256
batch_size: 32
seed: 12345
train-size: 100000
valid-size: 10000
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 8e-05
critic_lr: 8e-05
attention_type: configurable
n_head: 16
2025_06_21_05_34_01
Batch 99/3125, reward: 0.075, revenue_rate: 0.075, distance: 1.312, power: 0.0172, loss: 65.5023, lr:0.0000778, took: 22.2008s
Batch 199/3125, reward: 0.078, revenue_rate: 0.078, distance: 1.330, power: 0.0167, loss: -1.3980, lr:0.0000714, took: 22.5542s
Batch 299/3125, reward: 0.079, revenue_rate: 0.079, distance: 1.361, power: 0.0173, loss: -3.0226, lr:0.0000616, took: 22.6798s
Batch 399/3125, reward: 0.080, revenue_rate: 0.080, distance: 1.378, power: 0.0187, loss: -0.8697, lr:0.0000495, took: 23.2865s
Batch 499/3125, reward: 0.081, revenue_rate: 0.081, distance: 1.394, power: 0.0182, loss: 1.7302, lr:0.0000363, took: 22.8309s
Batch 599/3125, reward: 0.080, revenue_rate: 0.080, distance: 1.374, power: 0.0148, loss: -0.4298, lr:0.0000236, took: 22.5019s
Batch 699/3125, reward: 0.077, revenue_rate: 0.077, distance: 1.331, power: 0.0173, loss: -0.0020, lr:0.0000128, took: 22.6392s
Batch 799/3125, reward: 0.079, revenue_rate: 0.079, distance: 1.343, power: 0.0168, loss: 1.6370, lr:0.0000051, took: 22.6611s
Batch 899/3125, reward: 0.080, revenue_rate: 0.080, distance: 1.365, power: 0.0130, loss: 1.2924, lr:0.0000013, took: 23.1530s
Batch 999/3125, reward: 0.079, revenue_rate: 0.079, distance: 1.359, power: 0.0168, loss: -0.2641, lr:0.0000798, took: 22.8136s
Batch 1099/3125, reward: 0.094, revenue_rate: 0.094, distance: 1.585, power: 0.0182, loss: 4.9511, lr:0.0000785, took: 25.7732s
Batch 1199/3125, reward: 0.103, revenue_rate: 0.103, distance: 1.709, power: 0.0119, loss: -1.7385, lr:0.0000762, took: 27.0093s
Batch 1299/3125, reward: 0.119, revenue_rate: 0.119, distance: 1.613, power: -0.1124, loss: 0.1795, lr:0.0000729, took: 25.6733s
Batch 1399/3125, reward: 0.130, revenue_rate: 0.130, distance: 1.405, power: -0.2566, loss: -0.9236, lr:0.0000687, took: 24.0851s
Batch 1499/3125, reward: 0.131, revenue_rate: 0.131, distance: 1.395, power: -0.2579, loss: 1.4500, lr:0.0000637, took: 23.6498s
Batch 1599/3125, reward: 0.132, revenue_rate: 0.132, distance: 1.417, power: -0.2557, loss: -0.5972, lr:0.0000580, took: 24.0160s
Batch 1699/3125, reward: 0.133, revenue_rate: 0.133, distance: 1.427, power: -0.2551, loss: 1.0734, lr:0.0000519, took: 24.3579s
Batch 1799/3125, reward: 0.134, revenue_rate: 0.134, distance: 1.453, power: -0.2546, loss: -3.1415, lr:0.0000454, took: 24.3500s
Batch 1899/3125, reward: 0.134, revenue_rate: 0.134, distance: 1.440, power: -0.2547, loss: -0.1316, lr:0.0000388, took: 24.2519s
Batch 1999/3125, reward: 0.132, revenue_rate: 0.132, distance: 1.408, power: -0.2553, loss: 2.9319, lr:0.0000322, took: 24.5084s
Batch 2099/3125, reward: 0.133, revenue_rate: 0.133, distance: 1.434, power: -0.2553, loss: -0.4023, lr:0.0000259, took: 24.2358s
Batch 2199/3125, reward: 0.134, revenue_rate: 0.134, distance: 1.432, power: -0.2558, loss: -0.4198, lr:0.0000200, took: 24.3516s
Batch 2299/3125, reward: 0.134, revenue_rate: 0.134, distance: 1.458, power: -0.2547, loss: -0.3668, lr:0.0000146, took: 24.6577s
Batch 2399/3125, reward: 0.134, revenue_rate: 0.134, distance: 1.448, power: -0.2548, loss: -0.0431, lr:0.0000100, took: 24.2716s
Batch 2499/3125, reward: 0.135, revenue_rate: 0.135, distance: 1.459, power: -0.2540, loss: 0.1910, lr:0.0000062, took: 24.6141s
Batch 2599/3125, reward: 0.136, revenue_rate: 0.136, distance: 1.468, power: -0.2539, loss: -0.3291, lr:0.0000034, took: 24.8502s
Batch 2699/3125, reward: 0.136, revenue_rate: 0.136, distance: 1.481, power: -0.2535, loss: 0.8224, lr:0.0000017, took: 25.0771s
Batch 2799/3125, reward: 0.136, revenue_rate: 0.136, distance: 1.474, power: -0.2536, loss: 0.0113, lr:0.0000010, took: 25.0835s
Batch 2899/3125, reward: 0.134, revenue_rate: 0.134, distance: 1.453, power: -0.2542, loss: -0.8437, lr:0.0000799, took: 25.2645s
Batch 2999/3125, reward: 0.134, revenue_rate: 0.134, distance: 1.460, power: -0.2504, loss: 0.6670, lr:0.0000795, took: 24.6262s
Batch 3099/3125, reward: 0.097, revenue_rate: 0.097, distance: 1.552, power: -0.0075, loss: 0.1687, lr:0.0000788, took: 25.3996s
Epoch 0 mean epoch loss/reward: 2.1785, 0.1120, -0.7300, took: 909.7477s (24.1106s / 100 batches)
Batch 99/3125, reward: 0.113, revenue_rate: 0.113, distance: 1.754, power: -0.0146, loss: 0.8664, lr:0.0000776, took: 27.2248s
Batch 199/3125, reward: 0.134, revenue_rate: 0.134, distance: 1.514, power: -0.2320, loss: -1.5206, lr:0.0000764, took: 25.7188s
Batch 299/3125, reward: 0.134, revenue_rate: 0.134, distance: 1.448, power: -0.2544, loss: 0.2916, lr:0.0000749, took: 24.5714s
Batch 399/3125, reward: 0.136, revenue_rate: 0.136, distance: 1.474, power: -0.2542, loss: -0.5539, lr:0.0000731, took: 25.4287s
Batch 499/3125, reward: 0.135, revenue_rate: 0.135, distance: 1.685, power: -0.1662, loss: -0.8012, lr:0.0000712, took: 27.6264s
Batch 599/3125, reward: 0.136, revenue_rate: 0.136, distance: 1.617, power: -0.1996, loss: -0.3188, lr:0.0000690, took: 26.9003s
Batch 699/3125, reward: 0.114, revenue_rate: 0.114, distance: 1.659, power: -0.0555, loss: 2.1888, lr:0.0000666, took: 26.5309s
Batch 799/3125, reward: 0.135, revenue_rate: 0.135, distance: 1.544, power: -0.2260, loss: 0.6138, lr:0.0000640, took: 26.3453s
Batch 899/3125, reward: 0.138, revenue_rate: 0.138, distance: 1.492, power: -0.2531, loss: -0.1790, lr:0.0000613, took: 26.1351s
Batch 999/3125, reward: 0.139, revenue_rate: 0.139, distance: 1.504, power: -0.2530, loss: -0.0525, lr:0.0000584, took: 25.9413s
Batch 1099/3125, reward: 0.139, revenue_rate: 0.139, distance: 1.511, power: -0.2508, loss: -1.5098, lr:0.0000554, took: 26.6788s
Batch 1199/3125, reward: 0.136, revenue_rate: 0.136, distance: 1.591, power: -0.2016, loss: -1.0604, lr:0.0000522, took: 26.8124s
Batch 1299/3125, reward: 0.139, revenue_rate: 0.139, distance: 1.586, power: -0.2335, loss: -0.2529, lr:0.0000490, took: 27.0835s
Batch 1399/3125, reward: 0.140, revenue_rate: 0.140, distance: 1.550, power: -0.2465, loss: 0.4893, lr:0.0000458, took: 25.5910s
Batch 1499/3125, reward: 0.136, revenue_rate: 0.136, distance: 1.480, power: -0.2496, loss: -0.0251, lr:0.0000425, took: 25.1092s
Batch 1599/3125, reward: 0.141, revenue_rate: 0.141, distance: 1.558, power: -0.2481, loss: 0.4983, lr:0.0000392, took: 26.8163s
Batch 1699/3125, reward: 0.140, revenue_rate: 0.140, distance: 1.583, power: -0.2386, loss: -0.0671, lr:0.0000359, took: 27.1868s
Batch 1799/3125, reward: 0.142, revenue_rate: 0.142, distance: 1.578, power: -0.2484, loss: -0.7389, lr:0.0000326, took: 27.1280s
Batch 1899/3125, reward: 0.140, revenue_rate: 0.140, distance: 1.569, power: -0.2389, loss: 0.5062, lr:0.0000294, took: 27.0519s
Batch 1999/3125, reward: 0.139, revenue_rate: 0.139, distance: 1.540, power: -0.2468, loss: -0.2548, lr:0.0000263, took: 26.4699s
Batch 2099/3125, reward: 0.141, revenue_rate: 0.141, distance: 1.564, power: -0.2487, loss: -0.0042, lr:0.0000232, took: 26.4844s
Batch 2199/3125, reward: 0.141, revenue_rate: 0.141, distance: 1.572, power: -0.2506, loss: 0.2307, lr:0.0000203, took: 26.5995s
Batch 2299/3125, reward: 0.143, revenue_rate: 0.143, distance: 1.582, power: -0.2500, loss: -0.5126, lr:0.0000175, took: 27.3259s
Batch 2399/3125, reward: 0.144, revenue_rate: 0.144, distance: 1.611, power: -0.2483, loss: 0.1285, lr:0.0000149, took: 27.9372s
Batch 2499/3125, reward: 0.145, revenue_rate: 0.145, distance: 1.623, power: -0.2466, loss: 0.4312, lr:0.0000125, took: 28.6898s
Batch 2599/3125, reward: 0.146, revenue_rate: 0.146, distance: 1.631, power: -0.2460, loss: 0.2645, lr:0.0000103, took: 28.2168s
Batch 2699/3125, reward: 0.147, revenue_rate: 0.147, distance: 1.656, power: -0.2459, loss: 0.0213, lr:0.0000082, took: 29.4132s
Batch 2799/3125, reward: 0.146, revenue_rate: 0.146, distance: 1.626, power: -0.2466, loss: 0.5434, lr:0.0000064, took: 28.5445s
Batch 2899/3125, reward: 0.147, revenue_rate: 0.147, distance: 1.635, power: -0.2485, loss: 0.3635, lr:0.0000049, took: 28.6254s
Batch 2999/3125, reward: 0.147, revenue_rate: 0.147, distance: 1.650, power: -0.2478, loss: -0.0467, lr:0.0000036, took: 28.7000s
Batch 3099/3125, reward: 0.147, revenue_rate: 0.147, distance: 1.650, power: -0.2456, loss: 0.1614, lr:0.0000025, took: 29.0300s
Epoch 1 mean epoch loss/reward: 1.0850, 0.1254, -0.7422, took: 1004.1024s (25.5701s / 100 batches)
Batch 99/3125, reward: 0.147, revenue_rate: 0.147, distance: 1.663, power: -0.2461, loss: -0.1677, lr:0.0000016, took: 28.1289s
Batch 199/3125, reward: 0.147, revenue_rate: 0.147, distance: 1.640, power: -0.2470, loss: 0.2701, lr:0.0000012, took: 29.1392s
Batch 299/3125, reward: 0.146, revenue_rate: 0.146, distance: 1.624, power: -0.2474, loss: -0.2423, lr:0.0000010, took: 29.0285s
Batch 399/3125, reward: 0.146, revenue_rate: 0.146, distance: 1.700, power: -0.2203, loss: -1.8286, lr:0.0000800, took: 29.0690s
Batch 499/3125, reward: 0.150, revenue_rate: 0.150, distance: 1.751, power: -0.2245, loss: 0.7662, lr:0.0000799, took: 29.7770s
Batch 599/3125, reward: 0.149, revenue_rate: 0.149, distance: 1.664, power: -0.2489, loss: 0.1872, lr:0.0000797, took: 29.7506s
Batch 699/3125, reward: 0.150, revenue_rate: 0.150, distance: 1.702, power: -0.2460, loss: 0.7428, lr:0.0000795, took: 30.2463s
Batch 799/3125, reward: 0.143, revenue_rate: 0.143, distance: 1.668, power: -0.2152, loss: 0.2569, lr:0.0000792, took: 28.5004s
Batch 899/3125, reward: 0.152, revenue_rate: 0.152, distance: 1.733, power: -0.2425, loss: 0.7903, lr:0.0000788, took: 30.8811s
Batch 999/3125, reward: 0.147, revenue_rate: 0.147, distance: 1.657, power: -0.2474, loss: 0.8961, lr:0.0000784, took: 28.6195s
Batch 1099/3125, reward: 0.155, revenue_rate: 0.155, distance: 2.035, power: -0.1254, loss: -1.3030, lr:0.0000778, took: 33.3176s
Batch 1199/3125, reward: 0.152, revenue_rate: 0.152, distance: 2.171, power: -0.0540, loss: 0.1035, lr:0.0000773, took: 35.4627s
Batch 1299/3125, reward: 0.167, revenue_rate: 0.167, distance: 2.232, power: -0.1213, loss: -0.3888, lr:0.0000766, took: 37.2082s
Batch 1399/3125, reward: 0.141, revenue_rate: 0.141, distance: 1.929, power: -0.1003, loss: -2.9382, lr:0.0000759, took: 31.3318s
Batch 1499/3125, reward: 0.171, revenue_rate: 0.171, distance: 2.166, power: -0.1725, loss: 0.0515, lr:0.0000752, took: 35.8958s
Batch 1599/3125, reward: 0.188, revenue_rate: 0.188, distance: 2.417, power: -0.1725, loss: 1.3194, lr:0.0000744, took: 39.7393s
Batch 1699/3125, reward: 0.197, revenue_rate: 0.197, distance: 2.530, power: -0.1718, loss: 0.3919, lr:0.0000735, took: 41.1934s
Batch 1799/3125, reward: 0.217, revenue_rate: 0.217, distance: 2.958, power: -0.1085, loss: -0.6049, lr:0.0000725, took: 44.8366s
Batch 1899/3125, reward: 0.223, revenue_rate: 0.223, distance: 3.051, power: -0.1113, loss: 1.3007, lr:0.0000715, took: 45.7171s
Batch 1999/3125, reward: 0.255, revenue_rate: 0.255, distance: 3.454, power: -0.1223, loss: 0.0033, lr:0.0000705, took: 51.2768s
Batch 2099/3125, reward: 0.255, revenue_rate: 0.255, distance: 3.530, power: -0.1034, loss: -3.1974, lr:0.0000694, took: 51.3477s
Batch 2199/3125, reward: 0.278, revenue_rate: 0.278, distance: 3.810, power: -0.1095, loss: -0.5707, lr:0.0000682, took: 55.6517s
Batch 2299/3125, reward: 0.296, revenue_rate: 0.296, distance: 4.019, power: -0.1233, loss: -0.5351, lr:0.0000670, took: 57.4113s
Batch 2399/3125, reward: 0.278, revenue_rate: 0.278, distance: 3.762, power: -0.1153, loss: -0.9149, lr:0.0000658, took: 55.5608s
Batch 2499/3125, reward: 0.286, revenue_rate: 0.286, distance: 3.934, power: -0.0973, loss: -2.0443, lr:0.0000645, took: 55.0340s
Batch 2599/3125, reward: 0.313, revenue_rate: 0.313, distance: 4.300, power: -0.0946, loss: 0.0805, lr:0.0000631, took: 59.5167s
Batch 2699/3125, reward: 0.311, revenue_rate: 0.311, distance: 4.320, power: -0.0831, loss: 0.7232, lr:0.0000618, took: 58.9642s
Batch 2799/3125, reward: 0.344, revenue_rate: 0.344, distance: 4.790, power: -0.0914, loss: 5.5266, lr:0.0000604, took: 64.9019s
Batch 2899/3125, reward: 0.344, revenue_rate: 0.344, distance: 4.792, power: -0.0928, loss: 1.9573, lr:0.0000589, took: 64.8696s
Batch 2999/3125, reward: 0.353, revenue_rate: 0.353, distance: 4.859, power: -0.1022, loss: -1.7077, lr:0.0000574, took: 68.8698s
Batch 3099/3125, reward: 0.368, revenue_rate: 0.368, distance: 5.089, power: -0.1042, loss: 1.2497, lr:0.0000559, took: 67.0981s
Epoch 2 mean epoch loss/reward: 0.7313, 0.1568, -0.8109, took: 1557.6113s (31.5451s / 100 batches)
